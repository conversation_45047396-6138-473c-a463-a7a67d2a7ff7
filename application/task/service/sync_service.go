package service

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	brandapp "brandreviews/application/brand/appservice"
	branddto "brandreviews/application/brand/dto"
	couponapp "brandreviews/application/coupon/appservice"
	coupondto "brandreviews/application/coupon/dto"
	"brandreviews/config"
	brandentity "brandreviews/domain/brand/entity"
	couponentity "brandreviews/domain/coupon/entity"
	"brandreviews/infra/ecode"
	"brandreviews/infra/external_gateway/linkbuxlib"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SyncService interface {
	SyncCouponList(ctx *gin.Context) *ecode.Error
	SyncCouponListWithContext(ctx context.Context) error
	SyncBrandList(ctx *gin.Context) *ecode.Error
	SyncBrandListWithContext(ctx context.Context) error
}

type SyncServiceImpl struct {
	config        *config.Config
	logger        *zap.Logger
	couponService couponapp.CouponAppService
	brandService  brandapp.BrandAppService
}

func NewSyncService(
	config *config.Config,
	logger *zap.Logger,
	couponService couponapp.CouponAppService,
	brandService brandapp.BrandAppService,
) SyncService {
	return &SyncServiceImpl{
		config:        config,
		logger:        logger,
		couponService: couponService,
		brandService:  brandService,
	}
}

func (s *SyncServiceImpl) SyncCouponList(ctx *gin.Context) *ecode.Error {
	s.logger.Info("开始同步 coupon 信息")

	// 获取现有的优惠券
	existingCoupons, err := s.getAllExistingCoupons(ctx)
	if err != nil {
		s.logger.Error("获取现有优惠券失败", zap.Error(err))
		return err
	}

	// 获取现有的品牌信息
	existingBrands, err := s.getAllExistingBrands(ctx)
	if err != nil {
		s.logger.Error("获取现有品牌失败", zap.Error(err))
		return err
	}

	// 创建品牌域名映射，用于快速查找
	brandDomainMap := make(map[string]uint64)
	for _, brand := range existingBrands {
		if brand.Website != "" {
			domain := s.extractDomain(brand.Website)
			if domain != "" {
				brandDomainMap[domain] = brand.Id
			}
		}
	}

	// 创建coupon code映射，用于快速查找
	couponCodeMap := make(map[string]*couponentity.Coupon)
	for _, c := range existingCoupons {
		couponCodeMap[c.Code] = c
	}

	// 遍历配置中的优惠券账户
	for _, accountConfig := range s.config.Task.DataCollection.CouponsAccounts {
		platformType := accountConfig.Type
		var coupons []*couponentity.Coupon

		if platformType == "linkbux" {
			token := accountConfig.Token
			limit := accountConfig.Limit
			// 获取优惠券数据
			couponsData, err := s.fetchCouponsFromLinkBux(token, limit)
			if err != nil {
				s.logger.Error("linkbux获取优惠券数据失败", zap.Error(err))
				continue
			}
			coupons = s.toEntityCreateCoupon(couponsData, brandDomainMap)
		}

		// 创建或更新优惠券信息
		var createCoupons []*couponentity.Coupon
		uniqueCouponMap := make(map[string]bool)
		for _, coupon := range coupons {
			if _, exists := uniqueCouponMap[coupon.Code]; exists {
				continue
			}
			if _, exists := couponCodeMap[coupon.Code]; !exists {
				createCoupons = append(createCoupons, coupon)
			}
			uniqueCouponMap[coupon.Code] = true
		}

		// 批量创建新优惠券
		if len(createCoupons) > 0 {
			if err := s.batchCreateCoupons(ctx, createCoupons); err != nil {
				s.logger.Error("批量创建coupon失败",
					zap.String("platform_type", platformType),
					zap.Error(err))
			} else {
				s.logger.Info("成功创建优惠券",
					zap.String("platform_type", platformType),
					zap.Int("count", len(createCoupons)))
			}
		}
	}

	s.logger.Info("同步coupon信息完成")
	return nil
}

// SyncBrandList 同步品牌列表
func (s *SyncServiceImpl) SyncBrandList(ctx *gin.Context) *ecode.Error {
	s.logger.Info("开始同步品牌信息")

	// 获取现有的品牌信息
	existingBrands, err := s.getAllExistingBrands(ctx)
	if err != nil {
		s.logger.Error("获取现有品牌失败", zap.Error(err))
		return err
	}

	// 创建品牌slug和域名映射，用于快速查找和去重
	brandSlugMap := make(map[string]bool)
	brandDomainMap := make(map[string]bool)
	for _, brand := range existingBrands {
		brandSlugMap[brand.Slug] = true
		if brand.Website != "" {
			domain := s.extractDomain(brand.Website)
			if domain != "" {
				brandDomainMap[domain] = true
			}
		}
	}

	// 遍历配置中的商家账户
	var allBrands []*brandentity.Brand
	for _, accountConfig := range s.config.Task.DataCollection.MerchantsAccounts {
		platformType := accountConfig.Type
		var merchants []map[string]interface{}

		if platformType == "linkbux" {
			token := accountConfig.Token
			limit := accountConfig.Limit
			// 获取商家数据
			merchantData, err := linkbuxlib.BatchGetMerchants(token, limit, "")
			if err != nil {
				s.logger.Error("linkbux获取商家数据失败", zap.Error(err))
				continue
			}
			merchants = merchantData
		}

		// 将商家数据转换为品牌实体
		for _, merchant := range merchants {
			// 提取域名用于去重
			website, ok := merchant["website"].(string)
			if !ok || website == "" {
				continue
			}
			domain := s.extractDomain(website)
			if domain == "" {
				continue
			}

			// 检查域名是否已存在
			if _, exists := brandDomainMap[domain]; exists {
				continue
			}

			// 获取slug并检查是否已存在
			slug, ok := merchant["slug"].(string)
			if !ok || slug == "" {
				continue
			}
			if _, exists := brandSlugMap[slug]; exists {
				continue
			}

			// 创建品牌实体
			brand := &brandentity.Brand{
				Slug:          slug,
				Name:          merchant["name"].(string),
				Description:   merchant["description"].(string),
				Logo:          merchant["logo"].(string),
				Website:       website,
				AffiliateLink: merchant["affiliate_link"].(string),
				CategorySlug:  merchant["category_slug"].(string),
				Featured:      merchant["featured"].(bool),
				Active:        merchant["active"].(bool),
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}

			// 添加到待创建列表
			allBrands = append(allBrands, brand)
			// 更新映射以避免重复
			brandSlugMap[slug] = true
			brandDomainMap[domain] = true
		}
	}

	// 批量创建新品牌
	if len(allBrands) > 0 {
		successCount := 0
		errorCount := 0

		for _, brand := range allBrands {
			s.logger.Info("创建品牌",
				zap.String("slug", brand.Slug),
				zap.String("name", brand.Name))

			err := s.brandService.CreateBrand(ctx, brand)
			if err != nil {
				s.logger.Error("创建品牌失败",
					zap.String("slug", brand.Slug),
					zap.Error(err))
				errorCount++
			} else {
				successCount++
			}
		}

		s.logger.Info("品牌创建结果",
			zap.Int("total", len(allBrands)),
			zap.Int("success", successCount),
			zap.Int("error", errorCount))
	} else {
		s.logger.Info("没有新品牌需要创建")
	}

	s.logger.Info("同步品牌信息完成")
	return nil
}

// SyncBrandListWithContext 使用context.Context同步品牌列表
func (s *SyncServiceImpl) SyncBrandListWithContext(ctx context.Context) error {
	// 创建gin.Context
	ginCtx := &gin.Context{}
	// 将context.Context设置到gin.Context中
	ginCtx.Request = &http.Request{}
	ginCtx.Request = ginCtx.Request.WithContext(ctx)

	// 调用SyncBrandList方法
	if err := s.SyncBrandList(ginCtx); err != nil {
		return err
	}
	return nil
}

// getAllExistingCoupons 获取所有现有优惠券
func (s *SyncServiceImpl) getAllExistingCoupons(ctx *gin.Context) ([]*couponentity.Coupon, *ecode.Error) {
	// 使用分页方式获取所有优惠券，避免一次性加载过多数据
	var allCoupons []*couponentity.Coupon
	page := 1
	pageSize := 1000 // 每页1000条记录

	for {
		// 创建请求参数
		req := &coupondto.GetCouponListReq{
			Page:     page,
			PageSize: pageSize,
		}

		// 获取优惠券列表
		resp, err := s.couponService.GetCouponListByCondition(ctx, req)
		if err != nil {
			s.logger.Error("获取优惠券列表失败", zap.Int("page", page), zap.Error(err))
			return nil, err
		}

		// 如果没有数据，退出循环
		if len(resp.CouponList) == 0 {
			break
		}

		// 将DTO转换为实体
		for _, couponDto := range resp.CouponList {
			coupon := &couponentity.Coupon{
				Id:             couponDto.Id,
				Slug:           couponDto.Slug,
				Title:          couponDto.Title,
				Description:    couponDto.Description,
				Code:           couponDto.Code,
				CouponUrl:      couponDto.CouponUrl,
				DiscountType:   couponDto.DiscountType,
				DiscountValue:  couponDto.DiscountValue,
				MinOrderValue:  couponDto.MinOrderValue,
				MaxDiscount:    couponDto.MaxDiscount,
				Featured:       couponDto.Featured,
				Active:         couponDto.Active,
				Verified:       couponDto.Verified,
				StartDate:      couponDto.StartDate,
				EndDate:        couponDto.EndDate,
				UsageLimit:     couponDto.UsageLimit,
				UsedCount:      couponDto.UsedCount,
				UserUsageLimit: couponDto.UserUsageLimit,
				BrandId:        couponDto.BrandId,
				BrandSlug:      couponDto.BrandSlug,
				CategoryId:     couponDto.CategoryId,
				CategorySlug:   couponDto.CategorySlug,
			}
			allCoupons = append(allCoupons, coupon)
		}

		// 如果返回的数据少于页面大小，说明已经是最后一页
		if len(resp.CouponList) < pageSize {
			break
		}

		page++
	}

	s.logger.Info("成功获取所有现有优惠券", zap.Int("total", len(allCoupons)))
	return allCoupons, nil
}

// getAllExistingBrands 获取所有现有品牌
func (s *SyncServiceImpl) getAllExistingBrands(ctx *gin.Context) ([]*brandentity.Brand, *ecode.Error) {
	// 使用分页方式获取所有品牌，避免一次性加载过多数据
	var allBrands []*brandentity.Brand
	page := 1
	pageSize := 1000 // 每页1000条记录

	for {
		// 创建请求参数
		req := &branddto.GetBrandListReq{
			Page:     page,
			PageSize: pageSize,
		}

		// 获取品牌列表
		resp, err := s.brandService.GetBrandListByCondition(ctx, req)
		if err != nil {
			s.logger.Error("获取品牌列表失败", zap.Int("page", page), zap.Error(err))
			return nil, err
		}

		// 如果没有数据，退出循环
		if len(resp.BrandList) == 0 {
			break
		}

		// 将DTO转换为实体
		for _, brandDto := range resp.BrandList {
			brand := &brandentity.Brand{
				Id:           brandDto.Id,
				Slug:         brandDto.Slug,
				Name:         brandDto.Name,
				Description:  brandDto.Description,
				Logo:         brandDto.Logo,
				Website:      brandDto.Website,
				Featured:     brandDto.Featured,
				CategorySlug: brandDto.CategorySlug,
			}
			allBrands = append(allBrands, brand)
		}

		// 如果返回的数据少于页面大小，说明已经是最后一页
		if len(resp.BrandList) < pageSize {
			break
		}

		page++
	}

	s.logger.Info("成功获取所有现有品牌", zap.Int("total", len(allBrands)))
	return allBrands, nil
}

// extractDomain 从URL中提取域名
func (s *SyncServiceImpl) extractDomain(website string) string {
	if website == "" {
		return ""
	}

	// 如果没有协议前缀，添加http://
	if !strings.HasPrefix(website, "http://") && !strings.HasPrefix(website, "https://") {
		website = "http://" + website
	}

	parsedURL, err := url.Parse(website)
	if err != nil {
		s.logger.Warn("解析URL失败", zap.String("website", website), zap.Error(err))
		return ""
	}

	domain := strings.ToLower(parsedURL.Hostname())
	// 移除www前缀
	if strings.HasPrefix(domain, "www.") {
		domain = domain[4:]
	}

	return domain
}

// fetchCouponsFromLinkBux 从LinkBux获取优惠券数据
func (s *SyncServiceImpl) fetchCouponsFromLinkBux(token string, limit int) ([]map[string]interface{}, error) {
	s.logger.Info("开始从LinkBux获取优惠券数据", zap.String("token", token), zap.Int("limit", limit))

	// 使用LinkBux客户端批量获取优惠券数据
	couponsData, err := linkbuxlib.BatchGetCoupons(token, limit)
	if err != nil {
		s.logger.Error("LinkBux API调用失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("成功从LinkBux获取优惠券数据", zap.Int("count", len(couponsData)))
	return couponsData, nil
}

// batchCreateCoupons 批量创建优惠券
func (s *SyncServiceImpl) batchCreateCoupons(ctx *gin.Context, coupons []*couponentity.Coupon) error {
	successCount := 0
	errorCount := 0

	for _, coupon := range coupons {
		s.logger.Info("创建优惠券",
			zap.String("code", coupon.Code),
			zap.String("title", coupon.Title))

		err := s.couponService.CreateCoupon(ctx, coupon)
		if err != nil {
			s.logger.Error("创建优惠券失败",
				zap.String("code", coupon.Code),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}
	}

	s.logger.Info("优惠券创建结果",
		zap.Int("total", len(coupons)),
		zap.Int("success", successCount),
		zap.Int("error", errorCount))

	if errorCount > 0 {
		return fmt.Errorf("批量创建优惠券时有 %d 个错误", errorCount)
	}
	return nil
}

// toEntityCreateCoupon 将LinkBux数据转换为优惠券实体
func (s *SyncServiceImpl) toEntityCreateCoupon(createDataRows []map[string]interface{}, brandDomainMap map[string]uint64) []*couponentity.Coupon {
	var coupons []*couponentity.Coupon
	for _, data := range createDataRows {
		// 提取必要字段
		code, _ := data["code"].(string)
		title, _ := data["title"].(string)
		description, _ := data["description"].(string)
		couponUrl, _ := data["coupon_url"].(string)
		website, _ := data["website"].(string)
		discountStr, _ := data["discount"].(string)
		startDateStr, _ := data["start_date"].(string)
		endDateStr, _ := data["end_date"].(string)
		categorySlug, _ := data["category_slug"].(string)
		slug, _ := data["slug"].(string)

		// 如果没有code或title，跳过
		if code == "" || title == "" {
			continue
		}

		// 解析折扣信息
		discountValue, discountType := s.parseDiscountString(discountStr)

		// 解析日期
		var startDate, endDate time.Time
		var err error
		if startDateStr != "" {
			startDate, err = time.Parse("2006-01-02", startDateStr)
			if err != nil {
				s.logger.Warn("解析开始日期失败", zap.String("date", startDateStr), zap.Error(err))
				startDate = time.Now()
			}
		} else {
			startDate = time.Now()
		}

		if endDateStr != "" {
			endDate, err = time.Parse("2006-01-02", endDateStr)
			if err != nil {
				s.logger.Warn("解析结束日期失败", zap.String("date", endDateStr), zap.Error(err))
				// 默认有效期为30天
				endDate = time.Now().AddDate(1, 0, 0)
			}
		} else {
			// 默认有效期为30天
			endDate = time.Now().AddDate(1, 0, 0)
		}

		// 查找品牌ID
		var brandId uint64
		if website != "" {
			domain := s.extractDomain(website)
			if domain != "" {
				if id, exists := brandDomainMap[domain]; exists {
					brandId = id
				}
			}
		}

		// 创建优惠券实体
		coupon := &couponentity.Coupon{
			Slug:          slug,
			Title:         title,
			Description:   description,
			Code:          code,
			CouponUrl:     couponUrl,
			DiscountType:  discountType,
			DiscountValue: discountValue,
			Featured:      false,
			Active:        true,
			Verified:      true,
			StartDate:     uint64(startDate.Unix()),
			EndDate:       uint64(endDate.Unix()),
			BrandId:       brandId,
			CategorySlug:  categorySlug,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		coupons = append(coupons, coupon)
	}

	return coupons
}

// parseDiscountString 解析折扣字符串
func (s *SyncServiceImpl) parseDiscountString(discountStr string) (float64, string) {
	if discountStr == "" {
		return 0, "fixed"
	}

	// 移除所有空格
	discountStr = strings.ReplaceAll(discountStr, " ", "")

	// 检查是否为百分比折扣
	if strings.Contains(discountStr, "%") {
		// 移除百分号
		discountStr = strings.ReplaceAll(discountStr, "%", "")
		// 尝试解析为浮点数
		value, err := parseFloat(discountStr)
		if err != nil {
			s.logger.Warn("解析百分比折扣失败", zap.String("discount", discountStr), zap.Error(err))
			return 0, "percentage"
		}
		return value, "percentage"
	}

	// 检查是否为固定金额折扣
	if strings.Contains(discountStr, "$") || strings.Contains(discountStr, "¥") || strings.Contains(discountStr, "€") {
		// 移除货币符号
		discountStr = strings.ReplaceAll(discountStr, "$", "")
		discountStr = strings.ReplaceAll(discountStr, "¥", "")
		discountStr = strings.ReplaceAll(discountStr, "€", "")
		// 尝试解析为浮点数
		value, err := parseFloat(discountStr)
		if err != nil {
			s.logger.Warn("解析固定金额折扣失败", zap.String("discount", discountStr), zap.Error(err))
			return 0, "fixed"
		}
		return value, "fixed"
	}

	// 尝试直接解析为浮点数
	value, err := parseFloat(discountStr)
	if err != nil {
		s.logger.Warn("解析折扣失败", zap.String("discount", discountStr), zap.Error(err))
		return 0, "fixed"
	}

	// 如果值大于100，假设是固定金额折扣
	if value > 100 {
		return value, "fixed"
	}

	// 否则假设是百分比折扣
	return value, "percentage"
}

// parseFloat 辅助函数，解析字符串为浮点数
func parseFloat(s string) (float64, error) {
	return strconv.ParseFloat(s, 64)
}

func (s *SyncServiceImpl) SyncCouponListWithContext(ctx context.Context) error {
	// 创建gin.Context
	ginCtx := &gin.Context{}
	// 将context.Context设置到gin.Context中
	ginCtx.Request = &http.Request{}
	ginCtx.Request = ginCtx.Request.WithContext(ctx)

	// 调用SyncCouponList方法
	if err := s.SyncCouponList(ginCtx); err != nil {
		return err
	}
	return nil
}
