---
import MainLayout from '../layouts/MainLayout.astro';
import { siteConfig } from '../config/site';
import Api, { DealDetailResp, CategoryDetailResp } from '../lib/api';

const { containerWidth } = siteConfig;

// Get URL parameters for filtering and pagination
const url = Astro.url;
const selectedCategory = url.searchParams.get('category') || '';
const searchQuery = url.searchParams.get('search') || '';
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 12;

// Fetch deals from API
let allDeals: DealDetailResp[] = [];
let totalDeals = 0;
let totalPages = 1;

try {
  // Build API request parameters
  const apiParams: any = {
    page: currentPage,
    page_size: itemsPerPage,
    active: true
  };

  if (selectedCategory) apiParams.category = selectedCategory;
  if (searchQuery) apiParams.search = searchQuery;

  // Fetch deals from API
  const dealData = await Api.Deal.getDealList(apiParams);
  allDeals = dealData.deal_list || [];
  totalDeals = dealData.total || 0;
  totalPages = Math.ceil(totalDeals / itemsPerPage);

} catch (error) {
  console.error('Failed to fetch deals:', error);
  allDeals = [];
}

// Fetch categories for filter
let allCategories: CategoryDetailResp[] = [];
try {
  const categoryData = await Api.Category.getCategoryList({ page_size: 50 });
  allCategories = categoryData.category_list || [];
} catch (error) {
  console.error('Failed to fetch categories:', error);
  allCategories = [];
}

// Helper function to generate URL with params
function getFilterUrl(params: Record<string, string> = {}) {
  const newParams = new URLSearchParams();
  
  // Add existing params except page (we reset to page 1 when changing filters)
  if (selectedCategory && !('category' in params)) newParams.set('category', selectedCategory);
  if (searchQuery && !('search' in params)) newParams.set('search', searchQuery);
  
  // Add new params
  Object.entries(params).forEach(([key, value]) => {
    if (value) newParams.set(key, value);
  });
  
  return `${Astro.url.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`;
}

// Helper function for pagination URLs
function getPaginationUrl(page: number) {
  const params = new URLSearchParams();
  if (selectedCategory) params.set('category', selectedCategory);
  if (searchQuery) params.set('search', searchQuery);
  if (page > 1) params.set('page', page.toString());
  return `${Astro.url.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
}

// Format date helper
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Helper function to calculate remaining days
const calculateRemainingDays = (endDate: string): number => {
  const end = new Date(endDate);
  const now = new Date();
  const diffTime: number = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
};
---

<MainLayout
  title="Hot Deals | BrandReviews"
  description="Discover the best deals, discounts, and special offers on top tech products and services."
>
  <!-- Ultra-Enhanced Page Header with Advanced Ghibli Effects -->
  <section class="py-24 relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced ghibli-aurora" data-section="deals-header" style="background: linear-gradient(135deg, rgba(216, 116, 94, 0.9) 0%, rgba(228, 166, 114, 0.8) 30%, rgba(251, 191, 36, 0.9) 70%, rgba(245, 158, 11, 0.95) 100%);">
    <!-- Multi-layered atmospheric effects -->
    <div class="absolute inset-0 ghibli-depth-blur"></div>
    <div class="absolute inset-0 ghibli-shimmer opacity-40"></div>
    <div class="absolute top-0 left-0 w-full h-full ghibli-floating-orbs"></div>
    
    <!-- Dynamic gradient overlays -->
    <div class="absolute top-0 right-0 w-[500px] h-[500px] bg-gradient-radial from-white/25 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-0 left-0 w-[400px] h-[400px] bg-gradient-radial from-yellow-300/20 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -3s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[300px] bg-gradient-radial from-orange-200/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -6s;"></div>
    
    <!-- Advanced floating decorative elements -->
    <div class="absolute top-1/4 right-12 opacity-25 ghibli-float-physics ghibli-sparkle">
      <svg width="80" height="80" viewBox="0 0 100 100" fill="none" class="text-white">
        <circle cx="50" cy="50" r="40" fill="currentColor" opacity="0.3"/>
        <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.5"/>
        <circle cx="50" cy="50" r="10" fill="currentColor" opacity="0.8"/>
        <path d="M50,20 L55,35 L70,40 L55,45 L50,60 L45,45 L30,40 L45,35 Z" fill="currentColor" opacity="0.9"/>
      </svg>
    </div>
    <div class="absolute bottom-1/3 left-16 opacity-30 ghibli-float-physics ghibli-sparkle" style="animation-delay: -2s;">
      <svg width="60" height="60" viewBox="0 0 100 100" fill="none" class="text-white">
        <path d="M50,10 Q80,30 90,50 Q80,70 50,90 Q20,70 10,50 Q20,30 50,10 Z" fill="currentColor" opacity="0.6"/>
        <circle cx="50" cy="50" r="15" fill="currentColor" opacity="0.8"/>
      </svg>
    </div>
    <div class="absolute top-1/3 left-1/4 opacity-20 ghibli-float-physics ghibli-sparkle" style="animation-delay: -4s;">
      <svg width="50" height="50" viewBox="0 0 100 100" fill="none" class="text-white">
        <polygon points="50,5 61,35 91,35 68,57 79,91 50,70 21,91 32,57 9,35 39,35" fill="currentColor" opacity="0.7"/>
      </svg>
    </div>
    
    <div class="container mx-auto px-4 max-w-6xl text-center relative z-10">
      <h1 class="text-6xl md:text-7xl font-bold mb-8 text-white drop-shadow-2xl ghibli-text-glow">
        🔥 Hot Deals
      </h1>
      <p class="text-2xl md:text-3xl max-w-4xl mx-auto text-white/95 leading-relaxed mb-10 drop-shadow-lg">
        Discover amazing discounts and limited-time offers on the best tech products and services
      </p>
      
      <!-- Ultra-enhanced search bar with magical effects -->
      <div class="max-w-3xl mx-auto">
        <form method="GET" class="relative ghibli-magic-search">
          <div class="absolute inset-0 bg-gradient-to-r from-white/20 to-white/10 rounded-3xl blur-xl"></div>
          <input 
            type="text" 
            name="search"
            value={searchQuery}
            placeholder="Search for deals, brands, or categories..." 
            class="relative w-full pl-8 pr-20 py-6 rounded-3xl text-gray-900 border-0 focus:outline-none focus:ring-4 focus:ring-white/40 shadow-2xl backdrop-blur-lg bg-white/95 text-xl font-medium placeholder-gray-500"
          />
          <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white p-4 rounded-2xl transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-110 ghibli-button-magic">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  </section>

  <!-- Ultra-Enhanced Filter Navigation with Magical Effects -->
  <section class="py-12 relative overflow-hidden ghibli-glow-enhanced ghibli-crystal-surface sticky top-0 z-20" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 252, 232, 0.9) 50%, rgba(255, 248, 220, 0.95) 100%); backdrop-filter: blur(20px); border-bottom: 2px solid rgba(216, 116, 94, 0.2);">
    <div class="absolute inset-0 ghibli-particles-enhanced opacity-15"></div>
    <div class="absolute inset-0 ghibli-rainbow-shimmer opacity-10"></div>

    <div class="container mx-auto px-4 max-w-7xl relative z-10">
      <!-- Active filters display with magical styling -->
      {(selectedCategory || searchQuery) && (
        <div class="mb-8 flex flex-wrap items-center gap-4">
          <span class="font-bold text-xl text-ghibli-earth-800 flex items-center ghibli-text-sparkle">
            <svg class="w-6 h-6 mr-3 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
            </svg>
            Active Filters:
          </span>

          {searchQuery && (
            <div class="inline-flex items-center bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 px-6 py-3 rounded-3xl text-lg font-semibold shadow-lg ghibli-filter-badge">
              <span>Search: "{searchQuery}"</span>
              <a href={getFilterUrl({search: ''})} class="ml-3 text-orange-600 hover:text-orange-800 transition-colors ghibli-hover-bounce">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}

          {selectedCategory && (
            <div class="inline-flex items-center bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 px-6 py-3 rounded-3xl text-lg font-semibold shadow-lg ghibli-filter-badge">
              <span>Category: {selectedCategory}</span>
              <a href={getFilterUrl({category: ''})} class="ml-3 text-yellow-600 hover:text-yellow-800 transition-colors ghibli-hover-bounce">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}

          <a href="/deals" class="inline-flex items-center text-orange-600 hover:text-orange-800 text-lg font-bold transition-all duration-300 hover:translate-x-2 ghibli-magic-link">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Clear All Filters
          </a>
        </div>
      )}

      <!-- Ultra-Enhanced Category filters -->
      <div class="flex flex-wrap justify-center gap-3">
        <span class="font-bold text-xl text-ghibli-earth-800 flex items-center mr-4 ghibli-text-sparkle">
          <svg class="w-6 h-6 mr-3 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
          </svg>
          Categories:
        </span>

        <a
          href={getFilterUrl({category: ''})}
          class={`ghibli-category-filter ${!selectedCategory ? 'active' : ''}`}
        >
          🌟 All Categories
        </a>

        {allCategories.map(category => (
          <a
            href={getFilterUrl({category: category.name})}
            class={`ghibli-category-filter ${selectedCategory === category.name ? 'active' : ''}`}
          >
            {category.name}
          </a>
        ))}
      </div>
    </div>
  </section>

  <!-- Ultra-Enhanced Deals Grid with Advanced Ghibli Effects -->
  <section class="py-20 relative overflow-hidden ghibli-mouse-light ghibli-crystal-atmosphere" data-section="deals-grid" style="background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 0.9) 50%, rgba(254, 252, 232, 0.95) 100%);">
    <!-- Multi-layered background effects -->
    <div class="absolute inset-0 ghibli-wind opacity-20"></div>
    <div class="absolute inset-0 ghibli-fireflies opacity-25"></div>
    <div class="absolute top-1/4 right-1/4 w-[500px] h-[500px] bg-gradient-radial from-orange-200/15 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-1/4 left-1/4 w-[400px] h-[400px] bg-gradient-radial from-yellow-200/12 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -7s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[300px] bg-gradient-radial from-red-200/10 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -4s;"></div>

    <div class="container mx-auto px-4 max-w-7xl relative z-10">
      <div class="mb-12 text-center">
        <h2 class="text-5xl md:text-6xl font-bold text-ghibli-earth-800 mb-6 ghibli-text-glow">
          {selectedCategory ? `${selectedCategory} Deals` : "All Hot Deals"}
          {searchQuery && (
            <span class="text-orange-600"> for "{searchQuery}"</span>
          )}
        </h2>
        <p class="text-xl text-ghibli-earth-600 max-w-4xl mx-auto leading-relaxed">
          {totalDeals > 0 ? (
            <>Showing {allDeals.length} of {totalDeals} amazing deals</>
          ) : (
            <>No deals found matching your criteria</>
          )}
        </p>
      </div>

      {totalDeals > 0 ? (
        <div>
          <!-- Ultra-Enhanced Deals Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-16">
            {allDeals.map((deal, index) => (
              <article class="ghibli-deal-card group relative" data-deal-index={index}>
                <a href={deal.tracking_url || deal.origin_url} target="_blank" rel="noopener noreferrer" class="block h-full">
                  <!-- Ultra-enhanced deal card with magical effects -->
                  <div class="ghibli-glow-enhanced bg-white/90 backdrop-blur-lg rounded-3xl overflow-hidden h-full flex flex-col border-2 border-orange-200/50 shadow-2xl hover:shadow-3xl transition-all duration-700 hover:scale-105 ghibli-card-magic">

                    <!-- Deal image with advanced effects -->
                    <div class="relative h-48 overflow-hidden">
                      <img
                        src={deal.featured_image || `https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80`}
                        alt={deal.title}
                        class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-125 ghibli-image-magic"
                      />
                      <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                      <!-- Discount badge with magical glow -->
                      <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-2xl font-bold text-lg shadow-xl ghibli-discount-badge">
                        {deal.discount_percentage ? `${deal.discount_percentage}% OFF` : 'Special Offer'}
                      </div>

                      <!-- Remaining time indicator -->
                      {deal.end_date && calculateRemainingDays(deal.end_date) > 0 && (
                        <div class="absolute bottom-4 right-4 bg-black/80 text-white px-3 py-1 rounded-xl text-sm font-medium backdrop-blur-sm ghibli-time-badge">
                          {calculateRemainingDays(deal.end_date)} days left
                        </div>
                      )}
                    </div>

                    <!-- Deal content with enhanced styling -->
                    <div class="p-6 flex-grow flex flex-col">
                      <h3 class="text-xl font-bold mb-3 text-ghibli-earth-800 group-hover:text-orange-700 transition-colors duration-300 line-clamp-2 ghibli-title-magic">
                        {deal.title}
                      </h3>

                      <p class="text-ghibli-earth-600 mb-4 text-base leading-relaxed flex-grow line-clamp-3">
                        {deal.description || 'Amazing deal with great savings on quality products and services.'}
                      </p>

                      <!-- Deal metadata -->
                      <div class="space-y-3 mt-auto">
                        {deal.category && (
                          <div class="flex items-center text-sm text-ghibli-earth-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                            </svg>
                            {deal.category.name}
                          </div>
                        )}

                        {deal.end_date && (
                          <div class="flex items-center text-sm text-ghibli-earth-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                            </svg>
                            Expires: {formatDate(deal.end_date)}
                          </div>
                        )}

                        <!-- Enhanced CTA button -->
                        <div class="pt-2">
                          <span class="inline-flex items-center w-full justify-center bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-3 px-6 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform group-hover:scale-105 ghibli-cta-magic">
                            🔥 Grab This Deal
                            <svg class="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </a>
              </article>
            ))}
          </div>
        </div>
      ) : (
        <!-- Enhanced No Deals Found State -->
        <div class="ghibli-glow-enhanced bg-white/90 backdrop-blur-lg p-20 rounded-3xl text-center border-2 border-orange-200/50 shadow-2xl">
          <div class="mb-10">
            <svg class="w-24 h-24 mx-auto text-orange-400 ghibli-icon-float" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
            </svg>
          </div>
          <h3 class="text-4xl font-bold text-ghibli-earth-800 mb-6 ghibli-text-glow">No Deals Found</h3>
          <p class="text-xl text-ghibli-earth-600 mb-10 max-w-2xl mx-auto leading-relaxed">
            {selectedCategory || searchQuery
              ? `We couldn't find any deals matching your criteria. Try adjusting your filters or search terms.`
              : 'No deals are currently available. Check back soon for amazing offers!'
            }
          </p>
          {(selectedCategory || searchQuery) && (
            <a href="/deals" class="inline-flex items-center bg-gradient-to-r from-orange-500 to-red-500 text-white font-bold px-8 py-4 rounded-2xl transition-all duration-300 hover:shadow-xl hover:scale-105 ghibli-button-magic">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              View All Deals
            </a>
          )}
        </div>
      )}
    </div>
  </section>
</MainLayout>

<style>
  /* 2025 Ultra-Advanced Ghibli Visual Effects for Deals Page */

  /* Aurora Background Effect */
  .ghibli-aurora {
    position: relative;
    overflow: hidden;
  }

  .ghibli-aurora::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(255, 107, 107, 0.1) 0%,
      rgba(255, 142, 83, 0.1) 25%,
      rgba(255, 193, 7, 0.1) 50%,
      rgba(255, 107, 107, 0.1) 75%,
      rgba(255, 142, 83, 0.1) 100%);
    animation: auroraShift 8s ease-in-out infinite;
    z-index: 1;
  }

  @keyframes auroraShift {
    0%, 100% { transform: translateX(-100%) skewX(-15deg); opacity: 0.3; }
    50% { transform: translateX(100%) skewX(15deg); opacity: 0.7; }
  }

  /* Shimmer Effect */
  .ghibli-shimmer {
    position: relative;
    overflow: hidden;
  }

  .ghibli-shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%);
    animation: shimmerMove 3s ease-in-out infinite;
  }

  @keyframes shimmerMove {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Floating Orbs */
  .ghibli-floating-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .ghibli-floating-orbs::before,
  .ghibli-floating-orbs::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: floatOrbs 15s ease-in-out infinite;
  }

  .ghibli-floating-orbs::before {
    width: 60px;
    height: 60px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }

  .ghibli-floating-orbs::after {
    width: 40px;
    height: 40px;
    top: 60%;
    right: 15%;
    animation-delay: -7s;
  }

  @keyframes floatOrbs {
    0%, 100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.3; }
    25% { transform: translateY(-20px) translateX(10px) scale(1.1); opacity: 0.6; }
    50% { transform: translateY(-10px) translateX(-5px) scale(0.9); opacity: 0.8; }
    75% { transform: translateY(-30px) translateX(15px) scale(1.05); opacity: 0.4; }
  }

  /* Sparkle Effect */
  .ghibli-sparkle {
    position: relative;
  }

  .ghibli-sparkle::before,
  .ghibli-sparkle::after {
    content: '✨';
    position: absolute;
    font-size: 12px;
    animation: sparkleFloat 4s ease-in-out infinite;
    pointer-events: none;
  }

  .ghibli-sparkle::before {
    top: -10px;
    left: -10px;
    animation-delay: 0s;
  }

  .ghibli-sparkle::after {
    bottom: -10px;
    right: -10px;
    animation-delay: -2s;
  }

  @keyframes sparkleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
    25% { transform: translateY(-5px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-5px) rotate(270deg); opacity: 1; }
  }

  /* Text Glow Effect */
  .ghibli-text-glow {
    text-shadow:
      0 0 10px rgba(255, 255, 255, 0.5),
      0 0 20px rgba(255, 193, 7, 0.3),
      0 0 30px rgba(255, 142, 83, 0.2);
    animation: textGlowPulse 3s ease-in-out infinite;
  }

  @keyframes textGlowPulse {
    0%, 100% {
      text-shadow:
        0 0 10px rgba(255, 255, 255, 0.5),
        0 0 20px rgba(255, 193, 7, 0.3),
        0 0 30px rgba(255, 142, 83, 0.2);
    }
    50% {
      text-shadow:
        0 0 15px rgba(255, 255, 255, 0.8),
        0 0 25px rgba(255, 193, 7, 0.5),
        0 0 35px rgba(255, 142, 83, 0.4);
    }
  }

  /* Magic Search Bar */
  .ghibli-magic-search {
    position: relative;
  }

  .ghibli-magic-search::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #ff6b6b, #ffa726, #ffeb3b, #ff6b6b);
    border-radius: 2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    animation: magicBorder 3s linear infinite;
  }

  .ghibli-magic-search:hover::before {
    opacity: 0.7;
  }

  @keyframes magicBorder {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Enhanced Category Filters */
  .ghibli-category-filter {
    display: inline-block;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 700;
    color: #6b7280;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(254, 252, 232, 0.8) 100%);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 142, 83, 0.2);
    border-radius: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0.5rem;
    text-align: center;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    position: relative;
    overflow: hidden;
  }

  .ghibli-category-filter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  .ghibli-category-filter:hover {
    background: linear-gradient(135deg, rgba(255, 142, 83, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
    border-color: rgba(255, 142, 83, 0.4);
    color: #374151;
    transform: translateY(-3px) scale(1.05);
    box-shadow:
      0 8px 15px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 142, 83, 0.2) inset;
  }

  .ghibli-category-filter:hover::before {
    left: 100%;
  }

  .ghibli-category-filter.active {
    background: linear-gradient(135deg, #ff8e53 0%, #ffc107 100%);
    color: white;
    border-color: #ff8e53;
    transform: translateY(-3px) scale(1.08);
    box-shadow:
      0 10px 20px rgba(255, 142, 83, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.2) inset;
  }

  /* Crystal Surface Effect */
  .ghibli-crystal-surface {
    position: relative;
  }

  .ghibli-crystal-surface::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.1) 100%);
    backdrop-filter: blur(10px);
    z-index: 1;
  }

  /* Deal Card Magic Effects */
  .ghibli-deal-card {
    position: relative;
  }

  .ghibli-card-magic {
    position: relative;
    overflow: hidden;
  }

  .ghibli-card-magic::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 142, 83, 0.1), transparent);
    animation: cardRotate 8s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .ghibli-card-magic:hover::before {
    opacity: 1;
  }

  @keyframes cardRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Enhanced Image Magic */
  .ghibli-image-magic {
    position: relative;
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .ghibli-image-magic::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
      rgba(255, 107, 107, 0.1) 0%,
      rgba(255, 142, 83, 0.1) 50%,
      rgba(255, 193, 7, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .group:hover .ghibli-image-magic::after {
    opacity: 1;
  }

  /* Scroll animations */
  [data-deal-index] {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  [data-deal-index].animate-in {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  /* Staggered animation delays */
  [data-deal-index="0"].animate-in { transition-delay: 0ms; }
  [data-deal-index="1"].animate-in { transition-delay: 100ms; }
  [data-deal-index="2"].animate-in { transition-delay: 200ms; }
  [data-deal-index="3"].animate-in { transition-delay: 300ms; }
  [data-deal-index="4"].animate-in { transition-delay: 400ms; }
  [data-deal-index="5"].animate-in { transition-delay: 500ms; }
  [data-deal-index="6"].animate-in { transition-delay: 600ms; }
  [data-deal-index="7"].animate-in { transition-delay: 700ms; }
  [data-deal-index="8"].animate-in { transition-delay: 800ms; }
  [data-deal-index="9"].animate-in { transition-delay: 900ms; }
  [data-deal-index="10"].animate-in { transition-delay: 1000ms; }
  [data-deal-index="11"].animate-in { transition-delay: 1100ms; }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .ghibli-floating-orbs::before,
    .ghibli-floating-orbs::after {
      display: none;
    }

    .ghibli-category-filter {
      padding: 0.75rem 1.5rem;
      font-size: 0.9rem;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .ghibli-aurora::before,
    .ghibli-shimmer::after,
    .ghibli-floating-orbs::before,
    .ghibli-floating-orbs::after,
    .ghibli-sparkle::before,
    .ghibli-sparkle::after,
    .ghibli-card-magic::before {
      animation: none;
    }

    [data-deal-index] {
      transition: none;
    }
  }
</style>

<script>
  // 2025 Ultra-Advanced Ghibli Interactions for Deals Page
  document.addEventListener('DOMContentLoaded', () => {
    // Enhanced Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -80px 0px'
    };

    const dealObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');

          // Add magical sparkle effect on scroll into view
          const sparkleEffect = document.createElement('div');
          sparkleEffect.innerHTML = '✨';
          sparkleEffect.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 20px;
            animation: sparkleAppear 2s ease-out forwards;
            pointer-events: none;
            z-index: 100;
          `;
          entry.target.appendChild(sparkleEffect);

          setTimeout(() => {
            sparkleEffect.remove();
          }, 2000);
        }
      });
    }, observerOptions);

    // Observe all deal cards
    const dealCards = document.querySelectorAll('[data-deal-index]');
    dealCards.forEach(card => dealObserver.observe(card));

    // Advanced mouse tracking for enhanced glow effects
    let mouseX = 0;
    let mouseY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;

      // Update CSS custom properties for mouse-following effects
      document.documentElement.style.setProperty('--mouse-x', `${(mouseX / window.innerWidth) * 100}%`);
      document.documentElement.style.setProperty('--mouse-y', `${(mouseY / window.innerHeight) * 100}%`);

      // Create trailing sparkles on mouse move
      if (Math.random() > 0.95) {
        createSparkle(mouseX, mouseY);
      }
    });

    // Create magical sparkle effect
    function createSparkle(x, y) {
      const sparkle = document.createElement('div');
      sparkle.innerHTML = '✨';
      sparkle.style.cssText = `
        position: fixed;
        left: ${x}px;
        top: ${y}px;
        font-size: 12px;
        pointer-events: none;
        z-index: 9999;
        animation: sparkleTrail 1s ease-out forwards;
      `;
      document.body.appendChild(sparkle);

      setTimeout(() => {
        sparkle.remove();
      }, 1000);
    }

    // Enhanced category filter interactions
    const categoryFilters = document.querySelectorAll('.ghibli-category-filter');
    categoryFilters.forEach(filter => {
      filter.addEventListener('mouseenter', () => {
        filter.style.transform = 'translateY(-5px) scale(1.08)';

        // Add magical glow effect
        filter.style.boxShadow = `
          0 15px 30px rgba(255, 142, 83, 0.2),
          0 0 0 1px rgba(255, 142, 83, 0.3) inset,
          0 0 20px rgba(255, 193, 7, 0.3)
        `;
      });

      filter.addEventListener('mouseleave', () => {
        if (!filter.classList.contains('active')) {
          filter.style.transform = 'translateY(0) scale(1)';
          filter.style.boxShadow = '';
        }
      });
    });

    // Enhanced deal card interactions
    dealCards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        // Add magical particle effect
        const particles = document.createElement('div');
        particles.innerHTML = '✨🌟💫';
        particles.style.cssText = `
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 16px;
          animation: particleExplosion 1s ease-out forwards;
          pointer-events: none;
          z-index: 10;
        `;
        card.appendChild(particles);

        setTimeout(() => {
          particles.remove();
        }, 1000);
      });
    });

    // Add dynamic CSS animations
    const style = document.createElement('style');
    style.textContent = `
      @keyframes sparkleAppear {
        0% { transform: scale(0) rotate(0deg); opacity: 0; }
        50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
        100% { transform: scale(0) rotate(360deg); opacity: 0; }
      }

      @keyframes sparkleTrail {
        0% { transform: scale(1) translateY(0); opacity: 1; }
        100% { transform: scale(0) translateY(-20px); opacity: 0; }
      }

      @keyframes particleExplosion {
        0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
        50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.8; }
        100% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
      }
    `;
    document.head.appendChild(style);

    // Performance monitoring
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        console.log('🔥 Ultra-Enhanced Deals page with 2025 Ghibli effects loaded successfully!');
      });
    }
  });
</script>
