---
import MainLayout from '../layouts/MainLayout.astro';
import Api, { ArticleDetailResp, CategoryDetailResp, TagDetailResp } from '../lib/api';

// Fetch articles from API
let allArticles: ArticleDetailResp[] = [];
let totalArticles = 0;
let currentPage = 1;
let totalPages = 1;

try {
  // Get URL parameters for filtering and pagination
  const url = Astro.url;
  const selectedCategory = url.searchParams.get('category') || '';
  const selectedBrand = url.searchParams.get('brand') || '';
  const selectedTag = url.searchParams.get('tag') || '';
  const searchQuery = url.searchParams.get('search') || '';
  currentPage = parseInt(url.searchParams.get('page') || '1');
  const articlesPerPage = 6;

  // Build API request parameters
  const apiParams: any = {
    page: currentPage,
    page_size: articlesPerPage,
  };

  if (selectedCategory) apiParams.category = selectedCategory;
  if (selectedBrand) apiParams.brand = selectedBrand;
  if (searchQuery) apiParams.search = searchQuery;
  if (selectedTag) apiParams.tag = selectedTag;

  // Fetch articles from API
  const articleData = await Api.Article.getArticleList(apiParams);
  allArticles = articleData.article_list || [];
  totalArticles = articleData.total || 0;
  totalPages = Math.ceil(totalArticles / articlesPerPage);

} catch (error) {
  console.error('Failed to fetch articles:', error);
  allArticles = [];
}
// Fetch categories from API
let allCategories: CategoryDetailResp[] = [];
try {
  const categoryData = await Api.Category.getCategoryList({ page_size: 50 });
  allCategories = categoryData.category_list || [];
} catch (error) {
  console.error('Failed to fetch categories:', error);
  allCategories = [];
}

// Fetch popular tags from API
let popularTags: TagDetailResp[] = [];
try {
  const tagData = await Api.Tag.getTagList({ page_size: 20 });
  popularTags = tagData.tag_list || [];
} catch (error) {
  console.error('Failed to fetch tags:', error);
  popularTags = [];
}
// Get URL parameters for filtering
const url = Astro.url;
const selectedCategory = url.searchParams.get('category') || '';
const selectedBrand = url.searchParams.get('brand') || '';
const selectedTag = url.searchParams.get('tag') || '';
const searchQuery = url.searchParams.get('search') || '';

// Get unique brands from articles for filter
const brands = [...new Set(allArticles.map(article => article.brand?.name || 'Unknown'))].filter(brand => brand !== 'Unknown').sort();

// Helper function to generate URL with params
function getFilterUrl(params: Record<string, string> = {}) {
  const newParams = new URLSearchParams();

  // Add existing params except page (we reset to page 1 when changing filters)
  if (selectedBrand && !('brand' in params)) newParams.set('brand', selectedBrand);
  if (selectedCategory && !('category' in params)) newParams.set('category', selectedCategory);
  if (selectedTag && !('tag' in params)) newParams.set('tag', selectedTag);
  if (searchQuery && !('search' in params)) newParams.set('search', searchQuery);

  // Add new params
  Object.entries(params).forEach(([key, value]) => {
    if (value) newParams.set(key, value);
  });

  return `${Astro.url.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`;
}

// Helper function for pagination URLs
function getPaginationUrl(page: number) {
  const params = new URLSearchParams();
  if (selectedBrand) params.set('brand', selectedBrand);
  if (selectedCategory) params.set('category', selectedCategory);
  if (selectedTag) params.set('tag', selectedTag);
  if (searchQuery) params.set('search', searchQuery);
  if (page > 1) params.set('page', page.toString());
  return `${Astro.url.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
}

// Format date helper
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
---

<MainLayout
  title={selectedBrand ? `${selectedBrand} Articles | BrandReviews` : "All Articles | BrandReviews"}
  description="Browse our comprehensive collection of product reviews, buying guides, and tech comparisons to make informed purchasing decisions."
>
  <!-- Enhanced Page Header with Ghibli Effects -->
  <section class="py-20 relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced" data-section="articles-header" style="background: linear-gradient(135deg, rgba(142, 155, 94, 0.9) 0%, rgba(228, 166, 114, 0.8) 50%, rgba(216, 116, 94, 0.9) 100%);">
    <!-- Parallax background layers -->
    <div class="absolute inset-0 ghibli-depth-blur"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-white/20 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-radial from-white/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -5s;"></div>

    <!-- Floating decorative elements -->
    <div class="absolute top-1/4 left-10 opacity-20 ghibli-float-physics">
      <svg width="60" height="60" viewBox="0 0 100 100" fill="none" class="text-white">
        <rect x="20" y="20" width="60" height="60" rx="8" fill="currentColor" opacity="0.4"/>
        <rect x="30" y="30" width="40" height="40" rx="4" fill="currentColor" opacity="0.6"/>
        <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.8"/>
      </svg>
    </div>
    <div class="absolute bottom-1/3 right-16 opacity-25 ghibli-float-physics" style="animation-delay: -3s;">
      <svg width="40" height="40" viewBox="0 0 100 100" fill="none" class="text-white">
        <path d="M50,10 L60,40 L90,50 L60,60 L50,90 L40,60 L10,50 L40,40 Z" fill="currentColor" opacity="0.7"/>
      </svg>
    </div>

    <div class="container mx-auto px-4 max-w-6xl text-center relative z-10">
      <h1 class="text-5xl md:text-6xl font-bold mb-6 text-white drop-shadow-lg">
        {selectedBrand ? `${selectedBrand} Articles` : "All Articles"}
      </h1>
      <p class="text-xl md:text-2xl max-w-4xl mx-auto text-white/90 leading-relaxed">
        Explore our in-depth reviews, guides, and comparisons to help you make informed purchasing decisions
      </p>

      <!-- Enhanced search bar -->
      <div class="mt-8 max-w-2xl mx-auto">
        <form method="GET" class="relative">
          <input
            type="text"
            name="search"
            value={searchQuery}
            placeholder="Search articles, brands, or categories..."
            class="w-full pl-6 pr-16 py-4 rounded-2xl text-gray-900 border-0 focus:outline-none focus:ring-4 focus:ring-white/30 shadow-xl backdrop-blur-sm bg-white/95"
          />
          <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-ghibli-warm-600 hover:bg-ghibli-warm-700 text-white p-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  </section>
  
  <!-- Enhanced Filter Navigation with Ghibli Styling -->
  <section class="py-8 relative overflow-hidden ghibli-glow-enhanced sticky top-0 z-20" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 252, 232, 0.9) 100%); backdrop-filter: blur(10px); border-bottom: 1px solid rgba(142, 155, 94, 0.2);">
    <div class="absolute inset-0 ghibli-particles-enhanced opacity-20"></div>

    <div class="container mx-auto px-4 max-w-6xl relative z-10">
      <!-- Active filters display with enhanced styling -->
      {(selectedBrand || selectedCategory || selectedTag || searchQuery) && (
        <div class="mb-6 flex flex-wrap items-center gap-3">
          <span class="font-semibold text-ghibli-earth-700 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
            </svg>
            Active filters:
          </span>

          {searchQuery && (
            <div class="inline-flex items-center bg-gradient-to-r from-ghibli-spring-100 to-ghibli-spring-200 text-ghibli-spring-800 px-4 py-2 rounded-2xl text-sm font-medium shadow-sm">
              <span>Search: "{searchQuery}"</span>
              <a href={getFilterUrl({search: ''})} class="ml-2 text-ghibli-spring-600 hover:text-ghibli-spring-800 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}

          {selectedBrand && (
            <div class="inline-flex items-center bg-gradient-to-r from-ghibli-warm-100 to-ghibli-warm-200 text-ghibli-warm-800 px-4 py-2 rounded-2xl text-sm font-medium shadow-sm">
              <span>Brand: {selectedBrand}</span>
              <a href={getFilterUrl({brand: ''})} class="ml-2 text-ghibli-warm-600 hover:text-ghibli-warm-800 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}

          {selectedCategory && (
            <div class="inline-flex items-center bg-gradient-to-r from-ghibli-earth-100 to-ghibli-earth-200 text-ghibli-earth-800 px-4 py-2 rounded-2xl text-sm font-medium shadow-sm">
              <span>Category: {selectedCategory}</span>
              <a href={getFilterUrl({category: ''})} class="ml-2 text-ghibli-earth-600 hover:text-ghibli-earth-800 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}

          {selectedTag && (
            <div class="inline-flex items-center bg-gradient-to-r from-ghibli-sunset-100 to-ghibli-sunset-200 text-ghibli-sunset-800 px-4 py-2 rounded-2xl text-sm font-medium shadow-sm">
              <span>Tag: {selectedTag}</span>
              <a href={getFilterUrl({tag: ''})} class="ml-2 text-ghibli-sunset-600 hover:text-ghibli-sunset-800 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}

          <a href="/articles" class="text-ghibli-warm-600 hover:text-ghibli-warm-800 text-sm font-medium flex items-center transition-colors">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Clear all
          </a>
        </div>
      )}

      <!-- Enhanced Category filters -->
      <div class="flex flex-wrap items-center gap-3">
        <span class="font-semibold text-ghibli-earth-700 flex items-center">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
          </svg>
          Categories:
        </span>

        <a
          href={getFilterUrl({category: ''})}
          class={`px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 ${!selectedCategory
            ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-lg transform scale-105'
            : 'bg-white/80 border border-ghibli-earth-200 text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 shadow-sm hover:shadow-md'}`}
        >
          All Categories
        </a>

        {allCategories.map(category => (
          <a
            href={getFilterUrl({category: category.name})}
            class={`px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 ${selectedCategory === category.name
              ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-lg transform scale-105'
              : 'bg-white/80 border border-ghibli-earth-200 text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 shadow-sm hover:shadow-md'}`}
          >
            {category.name}
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Enhanced Articles List with Ghibli Effects -->
  <section class="py-16 relative overflow-hidden ghibli-mouse-light" data-section="articles-list" style="background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);">
    <!-- Background effects -->
    <div class="absolute inset-0 ghibli-wind opacity-30"></div>
    <div class="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-radial from-ghibli-spring-200/20 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-radial from-ghibli-warm-200/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -6s;"></div>

    <div class="container mx-auto px-4 max-w-7xl relative z-10">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Enhanced Main Content -->
        <div class="lg:col-span-3">
          <div class="mb-10 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h2 class="text-3xl md:text-4xl font-bold text-ghibli-earth-800 mb-2">
                {selectedBrand ? `${selectedBrand} Articles` : "All Articles"}
                {selectedCategory ? ` - ${selectedCategory}` : ""}
                {selectedTag ? ` - ${selectedTag}` : ""}
              </h2>
              <p class="text-ghibli-earth-600 text-lg">
                Discover insights and reviews from our expert team
              </p>
            </div>
            <div class="flex items-center gap-4">
              <p class="text-ghibli-earth-500 font-medium">
                Showing {allArticles.length} of {totalArticles} articles
              </p>
              <div class="flex items-center gap-2">
                <span class="text-sm text-ghibli-earth-500">Page</span>
                <span class="bg-ghibli-warm-100 text-ghibli-warm-800 px-3 py-1 rounded-full text-sm font-medium">
                  {currentPage} of {totalPages}
                </span>
              </div>
            </div>
          </div>

          {totalArticles === 0 ? (
            <div class="ghibli-glow-enhanced bg-white/80 backdrop-blur-sm p-12 rounded-3xl text-center border border-ghibli-earth-200 shadow-xl">
              <div class="mb-6">
                <svg class="w-16 h-16 mx-auto text-ghibli-earth-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p class="text-xl text-ghibli-earth-600 mb-6">No articles found matching your filters.</p>
              <a href="/articles" class="inline-flex items-center bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white font-medium px-6 py-3 rounded-2xl transition-all duration-300 hover:shadow-lg hover:scale-105">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                View all articles
              </a>
            </div>
          ) : (
          <div class="space-y-8">
              {allArticles.map((article, index) => (
                <article class="ghibli-glow-enhanced group relative bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden border border-ghibli-earth-200 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-[1.02]" data-article-index={index}>
                  <div class="flex flex-col lg:flex-row">
                    <div class="lg:w-2/5 relative overflow-hidden">
                      <img
                        src={article.featured_image || `https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`}
                        alt={article.title}
                        class="w-full h-64 lg:h-full object-cover transition-transform duration-700 group-hover:scale-110"
                      />
                      <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

                      <!-- Enhanced category badge -->
                      <span class="absolute top-4 left-4 bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white text-sm font-semibold px-4 py-2 rounded-2xl shadow-lg backdrop-blur-sm">
                        {article.category?.name || 'Article'}
                      </span>

                      <!-- Reading time indicator -->
                      <div class="absolute bottom-4 left-4 flex items-center text-white/90 text-sm">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                        </svg>
                        5 min read
                      </div>
                    </div>

                    <div class="lg:w-3/5 p-8 flex flex-col justify-between">
                      <div>
                        <h3 class="text-2xl lg:text-3xl font-bold mb-4 text-ghibli-earth-800 leading-tight">
                          <a href={`/article/${article.slug}`} class="hover:text-ghibli-warm-700 transition-colors duration-300">
                            {article.title}
                          </a>
                        </h3>
                        <p class="text-ghibli-earth-600 mb-6 text-lg leading-relaxed line-clamp-3">
                          {article.description || 'Discover insights and expert analysis in this comprehensive article.'}
                        </p>
                      </div>

                      <div class="space-y-4">
                        <!-- Article metadata -->
                        <div class="flex flex-wrap justify-between items-center text-sm text-ghibli-earth-500 gap-y-2">
                          <div class="flex items-center gap-4">
                            <span class="flex items-center">
                              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                              </svg>
                              {formatDate(article.publish_date)}
                            </span>
                            <span class="flex items-center">
                              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                              </svg>
                              By {article.author?.name || 'Editorial Team'}
                            </span>
                          </div>

                          {article.brand && (
                            <a
                              href={getFilterUrl({brand: article.brand.name})}
                              class="inline-flex items-center bg-gradient-to-r from-ghibli-earth-100 to-ghibli-earth-200 hover:from-ghibli-earth-200 hover:to-ghibli-earth-300 px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 hover:shadow-md"
                            >
                              <span class="text-ghibli-earth-700">{article.brand.name}</span>
                            </a>
                          )}
                        </div>

                        <!-- Enhanced Tags -->
                        {article.tags && article.tags.length > 0 && (
                          <div class="flex flex-wrap gap-2">
                            {article.tags.slice(0, 4).map(tag => (
                              <a
                                href={getFilterUrl({tag: tag.slug})}
                                class={`text-xs px-3 py-1.5 rounded-full font-medium transition-all duration-300 ${
                                  selectedTag === tag.slug
                                    ? 'bg-gradient-to-r from-ghibli-sunset-600 to-ghibli-sunset-700 text-white shadow-md'
                                    : 'bg-ghibli-sunset-100 hover:bg-ghibli-sunset-200 text-ghibli-sunset-800 hover:shadow-sm'
                                }`}
                              >
                                #{tag.name}
                              </a>
                            ))}
                            {article.tags.length > 4 && (
                              <span class="text-xs px-3 py-1.5 rounded-full bg-ghibli-earth-100 text-ghibli-earth-600 font-medium">
                                +{article.tags.length - 4} more
                              </span>
                            )}
                          </div>
                        )}

                        <!-- Read more button -->
                        <div class="pt-2">
                          <a
                            href={`/article/${article.slug}`}
                            class="inline-flex items-center text-ghibli-warm-600 hover:text-ghibli-warm-700 font-semibold transition-all duration-300 group-hover:translate-x-2"
                          >
                            Read full article
                            <svg class="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
            ))}
          </div>
          )}

          <!-- Enhanced Pagination -->
          {totalPages > 1 && (
          <div class="mt-16 flex justify-center">
            <nav class="inline-flex items-center gap-2">
                {currentPage > 1 && (
                  <a
                    href={getPaginationUrl(currentPage - 1)}
                    class="inline-flex items-center px-4 py-3 bg-white/80 backdrop-blur-sm border border-ghibli-earth-200 rounded-2xl text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                  </a>
                )}

                <div class="flex items-center gap-1">
                  {Array.from({length: Math.min(totalPages, 7)}, (_, i) => {
                    let page;
                    if (totalPages <= 7) {
                      page = i + 1;
                    } else if (currentPage <= 4) {
                      page = i + 1;
                    } else if (currentPage >= totalPages - 3) {
                      page = totalPages - 6 + i;
                    } else {
                      page = currentPage - 3 + i;
                    }

                    return (
                      <a
                        href={getPaginationUrl(page)}
                        class={`w-12 h-12 flex items-center justify-center rounded-2xl font-semibold transition-all duration-300 ${
                          page === currentPage
                            ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-lg transform scale-110'
                            : 'bg-white/80 backdrop-blur-sm border border-ghibli-earth-200 text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 shadow-sm hover:shadow-md'
                        }`}
                      >
                        {page}
                      </a>
                    );
                  })}
                </div>

                {currentPage < totalPages && (
                  <a
                    href={getPaginationUrl(currentPage + 1)}
                    class="inline-flex items-center px-4 py-3 bg-white/80 backdrop-blur-sm border border-ghibli-earth-200 rounded-2xl text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
                  >
                    Next
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                )}
            </nav>
          </div>
          )}
        </div>

        <!-- Enhanced Sidebar -->
        <div class="lg:col-span-1">
          <!-- Enhanced Quick Search -->
          <div class="ghibli-glow-enhanced bg-white/80 backdrop-blur-sm p-6 mb-8 rounded-3xl border border-ghibli-earth-200 shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-ghibli-earth-800 flex items-center">
              <svg class="w-5 h-5 mr-2 text-ghibli-warm-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Quick Search
            </h3>
            <form method="GET" class="relative">
              <input
                type="text"
                name="search"
                value={searchQuery}
                placeholder="Search articles..."
                class="w-full pl-4 pr-12 py-3 rounded-2xl border border-ghibli-earth-200 focus:outline-none focus:ring-2 focus:ring-ghibli-warm-500 focus:border-ghibli-warm-500 transition-all duration-300 bg-white/90"
              />
              <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-ghibli-earth-400 hover:text-ghibli-warm-600 transition-colors duration-300 p-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </form>
          </div>

          <!-- Enhanced Brand Filter -->
          <div class="ghibli-glow-enhanced bg-white/80 backdrop-blur-sm p-6 mb-8 rounded-3xl border border-ghibli-earth-200 shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-ghibli-earth-800 flex items-center">
              <svg class="w-5 h-5 mr-2 text-ghibli-warm-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Filter by Brand
            </h3>

            <!-- Enhanced Brand search -->
            <div class="relative mb-6">
              <input
                type="text"
                id="brandSearch"
                placeholder="Search brands..."
                class="w-full pl-4 pr-10 py-3 rounded-2xl border border-ghibli-earth-200 focus:outline-none focus:ring-2 focus:ring-ghibli-warm-500 focus:border-ghibli-warm-500 transition-all duration-300 bg-white/90"
              />
              <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-ghibli-earth-400 hover:text-ghibli-warm-600 transition-colors duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>

            <!-- Enhanced Popular Brands -->
            <div class="mb-4">
              <h4 class="font-semibold text-ghibli-earth-700 mb-3 text-sm flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                Popular Brands
              </h4>
              <div class="space-y-2">
                <a
                  href={getFilterUrl({brand: ''})}
                  class={`block w-full text-left px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 ${!selectedBrand
                    ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-md'
                    : 'text-ghibli-earth-700 hover:bg-ghibli-earth-50 border border-ghibli-earth-200'}`}
                >
                  All Brands
                </a>
                {brands.slice(0, 8).map(brand => (
                  <a
                    href={getFilterUrl({brand: brand})}
                    class={`block w-full text-left px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 ${selectedBrand === brand
                      ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-md'
                      : 'text-ghibli-earth-700 hover:bg-ghibli-earth-50 border border-ghibli-earth-200'}`}
                  >
                    {brand}
                  </a>
                ))}
              </div>
            </div>

            <!-- Enhanced View all brands link -->
            <a
              href="/brands"
              class="inline-flex items-center text-ghibli-warm-600 hover:text-ghibli-warm-700 text-sm font-semibold transition-all duration-300 hover:translate-x-1"
            >
              Browse all brands
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>

          <!-- Enhanced Popular Tags -->
          <div class="ghibli-glow-enhanced bg-white/80 backdrop-blur-sm p-6 mb-8 rounded-3xl border border-ghibli-earth-200 shadow-lg">
            <h3 class="text-xl font-bold mb-4 text-ghibli-earth-800 flex items-center">
              <svg class="w-5 h-5 mr-2 text-ghibli-warm-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
              </svg>
              Popular Tags
            </h3>
            <div class="flex flex-wrap gap-2 mb-4">
              {popularTags.slice(0, 10).map(tag => (
                <a
                  href={getFilterUrl({tag: tag.slug})}
                  class={`text-sm px-4 py-2 rounded-2xl font-medium transition-all duration-300 ${
                    selectedTag === tag.slug
                      ? 'bg-gradient-to-r from-ghibli-sunset-600 to-ghibli-sunset-700 text-white shadow-md transform scale-105'
                      : 'bg-ghibli-sunset-100 hover:bg-ghibli-sunset-200 text-ghibli-sunset-800 hover:shadow-sm'
                  }`}
                >
                  #{tag.name}
                </a>
              ))}
            </div>
            <a href="/tags" class="inline-flex items-center text-ghibli-warm-600 hover:text-ghibli-warm-700 text-sm font-semibold transition-all duration-300 hover:translate-x-1">
              View all tags
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </a>
          </div>

          <!-- Enhanced Newsletter -->
          <div class="ghibli-glow-enhanced bg-gradient-to-br from-ghibli-warm-50 to-ghibli-sunset-50 p-6 rounded-3xl border border-ghibli-warm-200 shadow-lg relative overflow-hidden">
            <!-- Decorative background -->
            <div class="absolute top-0 right-0 w-24 h-24 opacity-10">
              <svg viewBox="0 0 100 100" fill="none" class="w-full h-full text-ghibli-warm-400">
                <circle cx="50" cy="50" r="40" fill="currentColor"/>
                <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.7"/>
                <circle cx="50" cy="50" r="10" fill="currentColor" opacity="0.5"/>
              </svg>
            </div>

            <div class="relative z-10">
              <h3 class="text-xl font-bold mb-3 text-ghibli-earth-800 flex items-center">
                <svg class="w-5 h-5 mr-2 text-ghibli-warm-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Stay Updated
              </h3>
              <p class="text-ghibli-earth-600 mb-4 leading-relaxed">Subscribe to our newsletter to receive the latest product reviews and buying guides.</p>
              <form class="space-y-4">
                <div>
                  <input
                    type="email"
                    placeholder="Your email address"
                    class="w-full px-4 py-3 rounded-2xl border border-ghibli-warm-200 focus:outline-none focus:ring-2 focus:ring-ghibli-warm-500 focus:border-ghibli-warm-500 transition-all duration-300 bg-white/90"
                    required
                  />
                </div>
                <button
                  type="submit"
                  class="w-full bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 hover:from-ghibli-warm-700 hover:to-ghibli-warm-800 text-white font-semibold py-3 px-4 rounded-2xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105"
                >
                  Subscribe Now
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Enhanced CTA Section -->
  <section class="py-20 relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced" style="background: linear-gradient(135deg, rgba(142, 155, 94, 0.9) 0%, rgba(228, 166, 114, 0.8) 50%, rgba(216, 116, 94, 0.9) 100%);">
    <!-- Background effects -->
    <div class="absolute inset-0 ghibli-depth-blur"></div>
    <div class="absolute top-0 left-0 w-96 h-96 bg-gradient-radial from-white/20 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-0 right-0 w-80 h-80 bg-gradient-radial from-white/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -4s;"></div>

    <!-- Floating decorative elements -->
    <div class="absolute top-1/4 right-10 opacity-20 ghibli-float-physics">
      <svg width="50" height="50" viewBox="0 0 100 100" fill="none" class="text-white">
        <path d="M50,10 L60,40 L90,50 L60,60 L50,90 L40,60 L10,50 L40,40 Z" fill="currentColor" opacity="0.6"/>
      </svg>
    </div>
    <div class="absolute bottom-1/3 left-16 opacity-25 ghibli-float-physics" style="animation-delay: -2s;">
      <svg width="40" height="40" viewBox="0 0 100 100" fill="none" class="text-white">
        <circle cx="50" cy="50" r="35" fill="currentColor" opacity="0.5"/>
        <circle cx="50" cy="50" r="20" fill="currentColor" opacity="0.7"/>
      </svg>
    </div>

    <div class="container mx-auto px-4 max-w-6xl text-center relative z-10">
      <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-lg">Looking for Something Specific?</h2>
      <p class="text-xl md:text-2xl mb-10 max-w-4xl mx-auto text-white/90 leading-relaxed">Use our advanced search to find exactly what you're looking for.</p>

      <div class="max-w-2xl mx-auto">
        <form method="GET" action="/articles" class="relative">
          <input
            type="text"
            name="search"
            placeholder="Search for products, brands, or categories..."
            class="w-full pl-6 pr-20 py-5 rounded-3xl text-gray-900 border-0 focus:outline-none focus:ring-4 focus:ring-white/30 shadow-2xl backdrop-blur-sm bg-white/95 text-lg"
          />
          <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 hover:from-ghibli-warm-700 hover:to-ghibli-warm-800 text-white p-4 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </form>

        <!-- Quick links -->
        <div class="mt-8 flex flex-wrap justify-center gap-4">
          <a href="/categories" class="inline-flex items-center text-white/90 hover:text-white font-medium transition-colors duration-300">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
            </svg>
            Browse Categories
          </a>
          <a href="/brands" class="inline-flex items-center text-white/90 hover:text-white font-medium transition-colors duration-300">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            Popular Brands
          </a>
          <a href="/deals" class="inline-flex items-center text-white/90 hover:text-white font-medium transition-colors duration-300">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd" />
            </svg>
            Hot Deals
          </a>
        </div>
      </div>
    </div>
  </section>
</MainLayout>

<style>
  /* Enhanced Articles Page Styling */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Scroll animations */
  [data-article-index] {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  [data-article-index].animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Staggered animation delays */
  [data-article-index="0"].animate-in { transition-delay: 0ms; }
  [data-article-index="1"].animate-in { transition-delay: 100ms; }
  [data-article-index="2"].animate-in { transition-delay: 200ms; }
  [data-article-index="3"].animate-in { transition-delay: 300ms; }
  [data-article-index="4"].animate-in { transition-delay: 400ms; }
  [data-article-index="5"].animate-in { transition-delay: 500ms; }

  /* Enhanced hover effects */
  .ghibli-glow-enhanced:hover {
    transform: translateY(-4px) scale(1.01);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(248, 250, 252, 0.5);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(142, 155, 94, 0.6), rgba(228, 166, 114, 0.6));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(142, 155, 94, 0.8), rgba(228, 166, 114, 0.8));
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .ghibli-float-physics {
      animation-duration: 8s;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .ghibli-float-physics,
    .ghibli-particles-enhanced,
    .ghibli-dynamic-bg {
      animation: none;
    }

    [data-article-index] {
      transition: none;
    }
  }
</style>

<script>
  // Enhanced Articles Page Interactions
  document.addEventListener('DOMContentLoaded', () => {
    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const articleObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe all article cards
    const articles = document.querySelectorAll('[data-article-index]');
    articles.forEach(article => articleObserver.observe(article));

    // Enhanced brand search functionality
    const brandSearchInput = document.getElementById('brandSearch') as HTMLInputElement;
    if (brandSearchInput) {
      const allBrands = [
        'Apple', 'Samsung', 'Sony', 'Asus', 'LG', 'Google',
        'Xiaomi', 'Huawei', 'Dell', 'Logitech', 'Amazon', 'Microsoft',
        'Razer', 'HP', 'Acer', 'Lenovo', 'Canon', 'Nikon', 'Intel', 'AMD',
        'OnePlus', 'Motorola', 'Nokia', 'Oppo', 'Vivo', 'Realme'
      ];

      const brandContainer = brandSearchInput.closest('.ghibli-glow-enhanced')?.querySelector('.space-y-2');
      if (!brandContainer) return;

      const defaultDisplay = brandContainer.innerHTML;

      // Handle search input with debouncing
      let searchTimeout: NodeJS.Timeout;
      brandSearchInput.addEventListener('input', () => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
          const searchTerm = brandSearchInput.value.toLowerCase().trim();

          if (!searchTerm) {
            brandContainer.innerHTML = defaultDisplay;
            return;
          }

          const filteredBrands = allBrands.filter(brand =>
            brand.toLowerCase().includes(searchTerm)
          );

          if (filteredBrands.length > 0) {
            let html = `
              <a
                href="?brand="
                class="block w-full text-left px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 text-ghibli-earth-700 hover:bg-ghibli-earth-50 border border-ghibli-earth-200"
              >
                All Brands
              </a>
            `;

            filteredBrands.forEach(brand => {
              const isSelected = new URLSearchParams(window.location.search).get('brand') === brand;
              html += `
                <a
                  href="?brand=${encodeURIComponent(brand)}"
                  class="block w-full text-left px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 ${
                    isSelected
                      ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-md'
                      : 'text-ghibli-earth-700 hover:bg-ghibli-earth-50 border border-ghibli-earth-200'
                  }"
                >
                  ${brand}
                </a>
              `;
            });

            brandContainer.innerHTML = html;
          } else {
            brandContainer.innerHTML = `
              <p class="text-ghibli-earth-500 text-sm px-4 py-3 text-center">No brands found matching "${searchTerm}"</p>
              <a
                href="?brand="
                class="block w-full text-left px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 text-ghibli-earth-700 hover:bg-ghibli-earth-50 border border-ghibli-earth-200"
              >
                All Brands
              </a>
            `;
          }
        }, 300);
      });
    }

    // Mouse tracking for enhanced glow effects
    let mouseX = 0;
    let mouseY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;

      // Update CSS custom properties for mouse-following effects
      document.documentElement.style.setProperty('--mouse-x', `${(mouseX / window.innerWidth) * 100}%`);
      document.documentElement.style.setProperty('--mouse-y', `${(mouseY / window.innerHeight) * 100}%`);
    });

    // Performance monitoring
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        console.log('Enhanced Articles page loaded successfully');
      });
    }
  });
</script>