---
import MainLayout from '../layouts/MainLayout.astro';
import { siteConfig } from '../config/site';

const { containerWidth } = siteConfig;

// Mock brand data (expanded for demonstration)
const generateMockBrands = () => {
  const brands = [
    { 
      id: 1, 
      name: 'Apple', 
      logo: '/images/apple-logo.svg', 
      slug: 'apple',
      description: 'Apple product reviews, including iPhone, iPad, MacBook and Apple Watch.',
      articleCount: 32,
      featuredImage: '/images/brand-apple.jpg'
    },
    { 
      id: 2, 
      name: 'Samsung', 
      logo: '/images/samsung-logo.svg', 
      slug: 'samsung',
      description: 'Samsung electronics product reviews, including Galaxy phones, tablets, TVs and home appliances.',
      articleCount: 28,
      featuredImage: '/images/brand-samsung.jpg'
    },
    { 
      id: 3, 
      name: 'Sony', 
      logo: '/images/sony-logo.svg', 
      slug: 'sony',
      description: 'Sony product reviews, including PlayStation, cameras, headphones and TVs.',
      articleCount: 24,
      featuredImage: '/images/brand-sony.jpg'
    },
    { 
      id: 4, 
      name: 'Microsoft', 
      logo: '/images/microsoft-logo.svg', 
      slug: 'microsoft',
      description: 'Microsoft product reviews, including Surface devices, Xbox gaming consoles and Windows systems.',
      articleCount: 20,
      featuredImage: '/images/brand-microsoft.jpg'
    },
    { 
      id: 5, 
      name: 'Amazon', 
      logo: '/images/amazon-logo.svg', 
      slug: 'amazon',
      description: 'Amazon product reviews, including Kindle, Echo smart speakers and Fire TV.',
      articleCount: 18,
      featuredImage: '/images/brand-amazon.jpg'
    },
    { 
      id: 6, 
      name: 'Google', 
      logo: '/images/google-logo.svg', 
      slug: 'google',
      description: 'Google product reviews, including Pixel phones, Nest smart home devices and Chromebooks.',
      articleCount: 22,
      featuredImage: '/images/brand-google.jpg'
    },
    { 
      id: 7, 
      name: 'Xiaomi', 
      logo: '/images/xiaomi-logo.svg', 
      slug: 'xiaomi',
      description: 'Xiaomi product reviews, including smartphones, smart home devices and appliances.',
      articleCount: 16,
      featuredImage: '/images/brand-xiaomi.jpg'
    },
    { 
      id: 8, 
      name: 'Huawei', 
      logo: '/images/huawei-logo.svg', 
      slug: 'huawei',
      description: 'Huawei product reviews, including smartphones, laptops and wearable devices.',
      articleCount: 14,
      featuredImage: '/images/brand-huawei.jpg'
    },
    { 
      id: 9, 
      name: 'Dell', 
      logo: '/images/dell-logo.svg', 
      slug: 'dell',
      description: 'Dell product reviews, including XPS laptops, Alienware gaming laptops and monitors.',
      articleCount: 12,
      featuredImage: '/images/brand-dell.jpg'
    },
    { 
      id: 10, 
      name: 'LG', 
      logo: '/images/lg-logo.svg', 
      slug: 'lg',
      description: 'LG product reviews, including TVs, monitors, smartphones and home appliances.',
      articleCount: 10,
      featuredImage: '/images/brand-lg.jpg'
    },
    { 
      id: 11, 
      name: 'Asus', 
      logo: '/images/asus-logo.svg', 
      slug: 'asus',
      description: 'Asus product reviews, including ROG gaming devices, ZenBook laptops and motherboards.',
      articleCount: 9,
      featuredImage: '/images/brand-asus.jpg'
    },
    { 
      id: 12, 
      name: 'Bose', 
      logo: '/images/bose-logo.svg', 
      slug: 'bose',
      description: 'Bose product reviews, including headphones, speakers and audio systems.',
      articleCount: 8,
      featuredImage: '/images/brand-bose.jpg'
    },
    // Additional brands with numeric starting characters
    {
      id: 13,
      name: '1MORE',
      logo: '/images/1more-logo.svg',
      slug: '1more',
      description: '1MORE audio product reviews, including earphones and headphones.',
      articleCount: 5,
      featuredImage: '/images/brand-1more.jpg'
    },
    {
      id: 14,
      name: '3M',
      logo: '/images/3m-logo.svg',
      slug: '3m',
      description: '3M product reviews, including office supplies and tech accessories.',
      articleCount: 3,
      featuredImage: '/images/brand-3m.jpg'
    },
    {
      id: 15,
      name: '4Sevens',
      logo: '/images/4sevens-logo.svg',
      slug: '4sevens',
      description: '4Sevens flashlight and portable lighting product reviews.',
      articleCount: 2,
      featuredImage: '/images/brand-4sevens.jpg'
    },
  ];
  
  // Generate more sample brands to simulate a large dataset
  const extraBrands: Array<{
    id: number;
    name: string;
    logo: string;
    slug: string;
    description: string;
    articleCount: number;
    featuredImage: string;
  }> = [];
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  
  for (let i = 16; i <= 100; i++) {
    const letter = letters[Math.floor(Math.random() * letters.length)];
    extraBrands.push({
      id: i,
      name: `${letter}Brand${i}`,
      logo: `/images/placeholder-logo.svg`,
      slug: `brand-${i}`,
      description: `Sample brand ${i} product reviews.`,
      articleCount: Math.floor(Math.random() * 10) + 1,
      featuredImage: '/images/placeholder-brand.jpg'
    });
  }
  
  return [...brands, ...extraBrands];
};

const allBrands = generateMockBrands();

// Get URL parameters for filtering and pagination
const url = Astro.url;
const filterChar = url.searchParams.get('filter') || 'all';
const currentPage = parseInt(url.searchParams.get('page') || '1');
const brandsPerPage = 24;

// Get all possible filter characters (A-Z + 0-9)
const filterOptions = [
  ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)), // A-Z
  ...Array.from({ length: 10 }, (_, i) => String(i)) // 0-9
];

// Filter brands based on the selected character
let filteredBrands = allBrands;
if (filterChar !== 'all') {
  if (filterChar === '0-9') {
    // Filter for all brands starting with a number
    filteredBrands = allBrands.filter(brand => /^[0-9]/.test(brand.name));
  } else {
    // Filter for brands starting with the selected letter
    filteredBrands = allBrands.filter(brand => 
      brand.name.toUpperCase().startsWith(filterChar));
  }
}

// Sort filtered brands alphabetically
filteredBrands.sort((a, b) => a.name.localeCompare(b.name));

// Pagination logic
const totalBrands = filteredBrands.length;
const totalPages = Math.ceil(totalBrands / brandsPerPage);
const paginatedBrands = filteredBrands.slice(
  (currentPage - 1) * brandsPerPage, 
  currentPage * brandsPerPage
);

// Get popular brands for the featured section
const popularBrands = [...allBrands]
  .sort((a, b) => b.articleCount - a.articleCount)
  .slice(0, 6);

// Helper function to generate pagination URL
function getPaginationUrl(page, filter = filterChar) {
  const params = new URLSearchParams();
  if (filter !== 'all') params.set('filter', filter);
  if (page > 1) params.set('page', page.toString());
  return `${Astro.url.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
}

// Helper function to generate filter URL
function getFilterUrl(filter) {
  return getPaginationUrl(1, filter);
}
---

<MainLayout
  title="Brands | SmartReviews"
  description="Browse product reviews, comparisons and buying guides for major tech brands, helping you make informed purchasing decisions."
>
  <!-- Featured Brands Section -->
  <section class="py-12 bg-white">
    <div class={`container mx-auto px-4 ${containerWidth}`}>
      <h2 class="text-3xl font-bold mb-8 text-center">Featured Brands</h2>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {popularBrands.map(brand => (
          <a href={`/articles?brand=${encodeURIComponent(brand.name)}`} class="group">
            <div class="card overflow-hidden h-full flex flex-col hover:shadow-lg transition-shadow">
              <div class="relative pb-[60%] overflow-hidden">
                <img 
                  src={brand.featuredImage} 
                  alt={brand.name}
                  class="absolute inset-0 w-full h-full object-cover transition-transform group-hover:scale-105"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                  <div class="p-6 w-full">
                    <div class="flex items-center mb-2">
                      <img 
                        src={brand.logo} 
                        alt={`${brand.name} logo`}
                        class="h-8 w-auto object-contain bg-white/90 rounded-full p-1"
                      />
                      <h3 class="text-2xl font-bold text-white ml-2">{brand.name}</h3>
                    </div>
                    <div class="flex justify-between items-center">
                      <span class="text-white/90 text-sm">{brand.articleCount} articles</span>
                      <span class="text-white bg-purple-600/80 rounded-full px-3 py-1 text-sm font-medium group-hover:bg-purple-600 transition-colors">
                        Browse
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Alphabet and Number Filters -->
  <section class="py-6 bg-gray-50 sticky top-16 z-10 border-y border-gray-200">
    <div class={`container mx-auto px-4 ${containerWidth}`}>
      <div class="flex flex-wrap justify-center gap-2">
        <a 
          href="/brands"
          class={`px-3 py-1.5 rounded-md font-medium transition-colors ${filterChar === 'all' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-purple-100 hover:text-purple-700'}`}
        >
          All
        </a>
        
        {/* Numeric filter (0-9) */}
        <a 
          href={getFilterUrl('0-9')}
          class={`px-3 py-1.5 rounded-md font-medium transition-colors ${filterChar === '0-9' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-purple-100 hover:text-purple-700'}`}
        >
          0-9
        </a>
        
        {/* Alphabetical filters (A-Z) */}
        {filterOptions.filter(char => /[A-Z]/.test(char)).map(char => (
          <a 
            href={getFilterUrl(char)}
            class={`px-3 py-1.5 rounded-md font-medium transition-colors ${filterChar === char ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-purple-100 hover:text-purple-700'}`}
          >
            {char}
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Brand Listing -->
  <section class="py-12 bg-white">
    <div class={`container mx-auto px-4 ${containerWidth}`}>
      <div class="mb-8 flex justify-between items-center">
        <h2 class="text-2xl font-bold">
          {filterChar === 'all' 
            ? 'All Brands' 
            : filterChar === '0-9' 
              ? 'Brands 0-9' 
              : `Brands: ${filterChar}`}
        </h2>
        <p class="text-gray-500">
          Showing {paginatedBrands.length} of {totalBrands} brands
        </p>
      </div>
      
      {totalBrands === 0 ? (
        <div class="text-center py-12">
          <p class="text-xl text-gray-600">No brands found starting with '{filterChar}'.</p>
          <a href="/brands" class="mt-4 inline-block text-purple-600 hover:text-purple-700 font-medium">
            View all brands
          </a>
        </div>
      ) : (
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {paginatedBrands.map(brand => (
            <a href={`/articles?brand=${encodeURIComponent(brand.name)}`} class="card p-4 hover:shadow-md transition-shadow flex items-start gap-3">
              <img 
                src={brand.logo} 
                alt={`${brand.name} logo`}
                class="w-10 h-10 object-contain bg-gray-50 p-1 rounded-md"
              />
              <div>
                <h3 class="text-lg font-bold text-gray-900">{brand.name}</h3>
                <p class="text-sm text-gray-500">{brand.articleCount} articles</p>
              </div>
            </a>
          ))}
        </div>
      )}
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div class="mt-12 flex justify-center">
          <nav class="inline-flex rounded-md shadow" aria-label="Pagination">
            {/* Previous page button */}
            {currentPage > 1 && (
              <a 
                href={getPaginationUrl(currentPage - 1)} 
                class="py-2 px-4 border border-gray-300 bg-white rounded-l-md text-gray-700 hover:bg-gray-50"
              >
                Previous
              </a>
            )}
            
            {/* First page */}
            {currentPage > 3 && (
              <a 
                href={getPaginationUrl(1)} 
                class="py-2 px-4 border-t border-b border-l border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
              >
                1
              </a>
            )}
            
            {/* Ellipsis after first page */}
            {currentPage > 4 && (
              <span class="py-2 px-4 border-t border-b border-gray-300 bg-white text-gray-700">
                ...
              </span>
            )}
            
            {/* Page 1 */}
            {totalPages >= 1 && currentPage <= 3 && (
              <a 
                href={getPaginationUrl(1)} 
                class={`py-2 px-4 border-t border-b border-l border-gray-300 ${
                  currentPage === 1 
                    ? 'bg-purple-50 text-purple-700 font-medium' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                1
              </a>
            )}
            
            {/* Page 2 */}
            {totalPages >= 2 && (currentPage <= 3 || (currentPage >= totalPages - 2 && totalPages <= 5) || (currentPage >= 3 && currentPage <= 4)) && (
              <a 
                href={getPaginationUrl(2)} 
                class={`py-2 px-4 border-t border-b border-l border-gray-300 ${
                  currentPage === 2 
                    ? 'bg-purple-50 text-purple-700 font-medium' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                2
              </a>
            )}
            
            {/* Page 3 */}
            {totalPages >= 3 && (currentPage <= 4 || (currentPage >= totalPages - 2 && totalPages <= 6) || (currentPage >= 4 && currentPage <= 5)) && (
              <a 
                href={getPaginationUrl(3)} 
                class={`py-2 px-4 border-t border-b border-l border-gray-300 ${
                  currentPage === 3 
                    ? 'bg-purple-50 text-purple-700 font-medium' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                3
              </a>
            )}
            
            {/* Current page - 1 (when current page > 3) */}
            {currentPage > 4 && currentPage <= totalPages && (
              <a 
                href={getPaginationUrl(currentPage - 1)} 
                class="py-2 px-4 border-t border-b border-l border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
              >
                {currentPage - 1}
              </a>
            )}
            
            {/* Current page (when > 3) */}
            {currentPage > 3 && currentPage <= totalPages && (
              <a 
                href={getPaginationUrl(currentPage)} 
                class="py-2 px-4 border-t border-b border-l border-gray-300 bg-purple-50 text-purple-700 font-medium"
              >
                {currentPage}
              </a>
            )}
            
            {/* Current page + 1 */}
            {currentPage > 3 && currentPage < totalPages - 1 && (
              <a 
                href={getPaginationUrl(currentPage + 1)} 
                class="py-2 px-4 border-t border-b border-l border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
              >
                {currentPage + 1}
              </a>
            )}
            
            {/* Ellipsis before last page */}
            {currentPage < totalPages - 3 && (
              <span class="py-2 px-4 border-t border-b border-gray-300 bg-white text-gray-700">
                ...
              </span>
            )}
            
            {/* Last page (when not already showing) */}
            {currentPage < totalPages - 2 && (
              <a 
                href={getPaginationUrl(totalPages)} 
                class="py-2 px-4 border-t border-b border-l border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
              >
                {totalPages}
              </a>
            )}
            
            {/* Next page button */}
            {currentPage < totalPages && (
              <a 
                href={getPaginationUrl(currentPage + 1)} 
                class="py-2 px-4 border border-gray-300 bg-white rounded-r-md text-gray-700 hover:bg-gray-50"
              >
                Next
              </a>
            )}
          </nav>
        </div>
      )}
    </div>
  </section>
  
  <!-- Newsletter -->
  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white">
    <div class={`container mx-auto px-4 ${containerWidth} text-center`}>
      <h2 class="text-3xl md:text-4xl font-bold mb-6">Stay Informed with <span class="text-purple-300">Expert Reviews</span></h2>
      <p class="text-xl mb-8 max-w-3xl mx-auto">Join our newsletter to receive the latest product reviews, buying guides, and exclusive discount codes from your favorite brands.</p>
      
      <div class="mb-8 bg-white/10 rounded-lg p-6 max-w-2xl mx-auto">
        <form class="flex flex-col sm:flex-row gap-3">
          <input 
            type="email" 
            placeholder="Your email address" 
            class="flex-1 px-4 py-3 rounded-md text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500"
            required
          />
          <button 
            type="submit" 
            class="bg-purple-600 hover:bg-purple-500 text-white font-medium px-6 py-3 rounded-md transition-colors"
          >
            Subscribe
          </button>
        </form>
      </div>
      
      <h2 class="text-3xl md:text-4xl font-bold mb-6">Can't Find the Brand You're Looking For?</h2>
      <p class="text-xl mb-8 max-w-3xl mx-auto">Tell us which brand you want to learn about, and we'll provide expert reviews and recommendations.</p>
      <a 
        href="/contact" 
        class="inline-block bg-purple-500 text-white hover:bg-purple-600 font-medium px-6 py-3 rounded-md transition-colors"
      >
        Contact Us
      </a>
    </div>
  </section>
</MainLayout>