---
import MainLayout from '../layouts/MainLayout.astro';
import { siteConfig } from '../config/site';
import Api, { BrandDetailResp, CategoryDetailResp } from '../lib/api';

const { containerWidth } = siteConfig;

// Get URL parameters for filtering and pagination
const url = Astro.url;
const filterChar = url.searchParams.get('filter') || 'all';
const currentPage = parseInt(url.searchParams.get('page') || '1');
const selectedCategory = url.searchParams.get('category') || '';
const searchQuery = url.searchParams.get('search') || '';
const itemsPerPage = 20;

// Fetch brands from API
let allBrands: BrandDetailResp[] = [];
let totalBrands = 0;
let totalPages = 1;

try {
  // Build API request parameters
  const apiParams: any = {
    page: currentPage,
    page_size: itemsPerPage,
    active: true
  };

  if (selectedCategory) apiParams.category_id = parseInt(selectedCategory);
  if (searchQuery) apiParams.search = searchQuery;

  // Fetch brands from API
  const brandData = await Api.Brand.getBrandList(apiParams);
  allBrands = brandData.brand_list || [];
  totalBrands = brandData.total || 0;
  totalPages = Math.ceil(totalBrands / itemsPerPage);

} catch (error) {
  console.error('Failed to fetch brands:', error);
  allBrands = [];
}

// Fetch featured brands for the top section
let featuredBrands: BrandDetailResp[] = [];
try {
  const featuredData = await Api.Brand.getBrandList({ featured: true, page_size: 6 });
  featuredBrands = featuredData.brand_list || [];
} catch (error) {
  console.error('Failed to fetch featured brands:', error);
  featuredBrands = [];
}

// Fetch categories for filter
let allCategories: CategoryDetailResp[] = [];
try {
  const categoryData = await Api.Category.getCategoryList({ page_size: 50 });
  allCategories = categoryData.category_list || [];
} catch (error) {
  console.error('Failed to fetch categories:', error);
  allCategories = [];
}
// Filter brands based on the selected filter character
let filteredBrands = allBrands;
if (filterChar !== 'all') {
  if (filterChar === '0-9') {
    filteredBrands = allBrands.filter(brand => /^[0-9]/.test(brand.name));
  } else {
    filteredBrands = allBrands.filter(brand =>
      brand.name.toLowerCase().startsWith(filterChar.toLowerCase())
    );
  }
}

// Generate filter options based on available brands
const filterOptions = ['all', '0-9'];
const usedLetters = new Set();
allBrands.forEach(brand => {
  const firstChar = brand.name.charAt(0).toUpperCase();
  if (/[A-Z]/.test(firstChar)) {
    usedLetters.add(firstChar);
  }
});
filterOptions.push(...Array.from(usedLetters).sort());

// Helper function to generate filter URL
function getFilterUrl(filter: string): string {
  const params = new URLSearchParams();
  if (filter !== 'all') params.set('filter', filter);
  if (selectedCategory) params.set('category', selectedCategory);
  if (searchQuery) params.set('search', searchQuery);
  return `/brands${params.toString() ? '?' + params.toString() : ''}`;
}

// Helper function to generate pagination URL
function getPaginationUrl(page: number): string {
  const params = new URLSearchParams();
  if (page > 1) params.set('page', page.toString());
  if (filterChar !== 'all') params.set('filter', filterChar);
  if (selectedCategory) params.set('category', selectedCategory);
  if (searchQuery) params.set('search', searchQuery);
  return `/brands${params.toString() ? '?' + params.toString() : ''}`;
}

---

<MainLayout
  title="Brands | SmartReviews"
  description="Browse product reviews, comparisons and buying guides for major tech brands, helping you make informed purchasing decisions."
>
  <!-- Featured Brands Section -->
  <section class="py-20 bg-gradient-to-b from-ghibli-spring-50/60 to-white relative overflow-hidden">
    <!-- Enhanced Ghibli-inspired decorative elements -->
    <div class="absolute -top-20 left-1/4 w-80 h-80 bg-ghibli-spring-200/30 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 right-1/4 w-96 h-96 bg-ghibli-warm-100/40 rounded-full blur-3xl"></div>

    <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
      <div class="text-center mb-16">
        <span class="inline-block px-5 py-2 text-sm font-medium text-ghibli-forest-800 bg-gradient-to-r from-ghibli-spring-100 to-ghibli-spring-200 rounded-full mb-4 shadow-md">Featured Collection</span>
        <h2 class="text-3xl md:text-5xl font-ghibliHeading font-bold text-ghibli-forest-800 relative inline-block">
          Featured Brands
          <span class="absolute -bottom-3 left-0 h-1.5 w-24 bg-gradient-to-r from-ghibli-warm-400 to-ghibli-warm-500 rounded-full"></span>
        </h2>
      </div>

      {featuredBrands.length > 0 ? (
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredBrands.map((brand, index) => (
            <a href={`/brand/${brand.slug}`} class="group">
              <div class="bg-white rounded-3xl shadow-ghibli overflow-hidden border border-ghibli-earth-100/60 h-full flex flex-col hover:shadow-ghibli-hover transform hover:-translate-y-2 transition-all duration-500 relative">
                <!-- Subtle glow effect on hover -->
                <div class="absolute -inset-0.5 bg-gradient-to-r from-ghibli-spring-400/20 to-ghibli-warm-400/20 rounded-3xl opacity-0 group-hover:opacity-100 blur-md transition-all duration-700"></div>

                <div class="relative pb-[60%] overflow-hidden">
                  <img
                    src={brand.logo || `/images/brand-placeholder-${(index % 3) + 1}.jpg`}
                    alt={brand.name}
                    class="absolute inset-0 w-full h-full object-contain p-8 transition-transform group-hover:scale-105"
                  />
                  <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/20 to-transparent"></div>
                </div>

                <div class="p-6 flex-grow flex flex-col relative">
                  <h3 class="text-xl font-ghibliHeading font-bold mb-3 text-ghibli-forest-800">
                    {brand.name}
                  </h3>
                  <p class="text-ghibli-earth-600 mb-4 flex-grow line-clamp-2">{brand.description}</p>
                  <div class="flex justify-between items-center text-sm">
                    <span class="text-ghibli-earth-500">Featured Brand</span>
                    <span class="text-ghibli-warm-600 font-medium group-hover:translate-x-1 transition-transform duration-300 flex items-center">
                      Explore
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            </a>
          ))}
        </div>
      ) : (
        <div class="text-center py-12">
          <p class="text-ghibli-earth-600 text-lg">No featured brands available at the moment.</p>
        </div>
      )}
    </div>
  </section>
  
  <!-- Enhanced Search and Filter Section -->
  <section class="py-12 bg-white relative overflow-hidden">
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-ghibli-spring-100/20 to-transparent blur-3xl"></div>

    <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
      <!-- Search and Category Filter -->
      <div class="mb-8 flex flex-col md:flex-row gap-4 items-center justify-between">
        <div class="flex-1 max-w-md">
          <form method="GET" class="relative">
            <input
              type="text"
              name="search"
              value={searchQuery}
              placeholder="Search brands..."
              class="w-full px-4 py-3 pl-10 rounded-2xl border border-ghibli-earth-200 focus:outline-none focus:ring-2 focus:ring-ghibli-warm-400 focus:border-transparent"
            />
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-ghibli-earth-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            {selectedCategory && <input type="hidden" name="category" value={selectedCategory} />}
            {filterChar !== 'all' && <input type="hidden" name="filter" value={filterChar} />}
          </form>
        </div>

        <div class="flex gap-4">
          <select
            name="category"
            onchange="window.location.href = this.value"
            class="px-4 py-3 rounded-2xl border border-ghibli-earth-200 focus:outline-none focus:ring-2 focus:ring-ghibli-warm-400 bg-white"
          >
            <option value={getPaginationUrl(1)}>All Categories</option>
            {allCategories.map(category => (
              <option
                value={getPaginationUrl(1) + (getPaginationUrl(1).includes('?') ? '&' : '?') + `category=${category.id}`}
                selected={selectedCategory === category.id.toString()}
              >
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <!-- Alphabet and Number Filters -->
      <div class="flex flex-wrap justify-center gap-2 mb-8">
        <a
          href={getFilterUrl('all')}
          class={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${filterChar === 'all' ? 'bg-ghibli-warm-500 text-white shadow-lg' : 'bg-white text-ghibli-earth-700 hover:bg-ghibli-warm-100 hover:text-ghibli-warm-700 border border-ghibli-earth-200'}`}
        >
          All
        </a>

        <a
          href={getFilterUrl('0-9')}
          class={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${filterChar === '0-9' ? 'bg-ghibli-warm-500 text-white shadow-lg' : 'bg-white text-ghibli-earth-700 hover:bg-ghibli-warm-100 hover:text-ghibli-warm-700 border border-ghibli-earth-200'}`}
        >
          0-9
        </a>

        {filterOptions.filter(char => /[A-Z]/.test(char)).map(char => (
          <a
            href={getFilterUrl(char)}
            class={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${filterChar === char ? 'bg-ghibli-warm-500 text-white shadow-lg' : 'bg-white text-ghibli-earth-700 hover:bg-ghibli-warm-100 hover:text-ghibli-warm-700 border border-ghibli-earth-200'}`}
          >
            {char}
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Enhanced Brand Listing -->
  <section class="py-20 bg-gradient-to-b from-white to-ghibli-earth-50/30 relative overflow-hidden">
    <div class="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-radial from-ghibli-warm-100/20 to-transparent blur-3xl"></div>

    <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
      <div class="mb-12 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 class="text-3xl md:text-4xl font-ghibliHeading font-bold text-ghibli-forest-800 mb-2">
            {filterChar === 'all'
              ? 'All Brands'
              : filterChar === '0-9'
                ? 'Brands 0-9'
                : `Brands: ${filterChar}`}
            {selectedCategory && (
              <span class="text-ghibli-warm-600"> - {allCategories.find(c => c.id.toString() === selectedCategory)?.name}</span>
            )}
          </h2>
          <p class="text-ghibli-earth-600 text-lg">
            Discover trusted brands and their products
          </p>
        </div>
        <div class="flex items-center gap-4">
          <p class="text-ghibli-earth-500 font-medium">
            Showing {filteredBrands.length} of {totalBrands} brands
          </p>
        </div>
      </div>

      {filteredBrands.length === 0 ? (
        <div class="text-center py-20">
          <div class="mb-6">
            <svg class="mx-auto h-16 w-16 text-ghibli-earth-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <h3 class="text-2xl font-ghibliHeading font-bold text-ghibli-forest-800 mb-4">No brands found</h3>
          <p class="text-ghibli-earth-600 mb-6">
            {searchQuery ? `No brands found matching "${searchQuery}"` :
             filterChar !== 'all' ? `No brands found starting with "${filterChar}"` :
             'No brands available at the moment'}
          </p>
          <a href="/brands" class="inline-flex items-center bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white font-medium px-6 py-3 rounded-2xl transition-all duration-300 hover:shadow-lg hover:scale-105">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            View all brands
          </a>
        </div>
      ) : (
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredBrands.map((brand, index) => (
            <a href={`/brand/${brand.slug}`} class="group">
              <div class="bg-white rounded-2xl shadow-ghibli p-6 hover:shadow-ghibli-hover transition-all duration-300 hover:-translate-y-1 border border-ghibli-earth-100/60 h-full flex flex-col relative">
                <!-- Subtle glow effect on hover -->
                <div class="absolute -inset-0.5 bg-gradient-to-r from-ghibli-spring-400/20 to-ghibli-warm-400/20 rounded-2xl opacity-0 group-hover:opacity-100 blur-md transition-all duration-700"></div>

                <div class="flex items-start gap-4 relative z-10">
                  <div class="w-16 h-16 flex-shrink-0 bg-ghibli-earth-50 rounded-xl p-2 group-hover:bg-ghibli-spring-100 transition-colors">
                    <img
                      src={brand.logo || `/images/brand-placeholder.svg`}
                      alt={`${brand.name} logo`}
                      class="w-full h-full object-contain"
                    />
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-lg font-ghibliHeading font-bold text-ghibli-forest-800 mb-2 group-hover:text-ghibli-warm-600 transition-colors">
                      {brand.name}
                    </h3>
                    <p class="text-sm text-ghibli-earth-600 line-clamp-2 mb-3">
                      {brand.description || 'Discover products and reviews from this brand'}
                    </p>
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-ghibli-earth-500 bg-ghibli-earth-100 px-2 py-1 rounded-full">
                        {brand.category_slug || 'Brand'}
                      </span>
                      <span class="text-ghibli-warm-600 text-sm font-medium group-hover:translate-x-1 transition-transform duration-300 flex items-center">
                        View
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </a>
          ))}
        </div>
      )}

      {/* Enhanced Pagination */}
      {totalPages > 1 && (
        <div class="mt-16 flex justify-center">
          <nav class="flex items-center gap-2" aria-label="Pagination">
            {/* Previous page button */}
            {currentPage > 1 ? (
              <a
                href={getPaginationUrl(currentPage - 1)}
                class="flex items-center px-4 py-2 text-ghibli-earth-600 bg-white border border-ghibli-earth-200 rounded-xl hover:bg-ghibli-warm-50 hover:text-ghibli-warm-600 transition-all duration-300"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </a>
            ) : (
              <span class="flex items-center px-4 py-2 text-ghibli-earth-300 bg-ghibli-earth-50 border border-ghibli-earth-100 rounded-xl cursor-not-allowed">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </span>
            )}

            {/* Page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <a
                  href={getPaginationUrl(pageNum)}
                  class={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                    currentPage === pageNum
                      ? 'bg-ghibli-warm-500 text-white shadow-lg'
                      : 'text-ghibli-earth-600 bg-white border border-ghibli-earth-200 hover:bg-ghibli-warm-50 hover:text-ghibli-warm-600'
                  }`}
                >
                  {pageNum}
                </a>
              );
            })}

            {/* Next page button */}
            {currentPage < totalPages ? (
              <a
                href={getPaginationUrl(currentPage + 1)}
                class="flex items-center px-4 py-2 text-ghibli-earth-600 bg-white border border-ghibli-earth-200 rounded-xl hover:bg-ghibli-warm-50 hover:text-ghibli-warm-600 transition-all duration-300"
              >
                Next
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            ) : (
              <span class="flex items-center px-4 py-2 text-ghibli-earth-300 bg-ghibli-earth-50 border border-ghibli-earth-100 rounded-xl cursor-not-allowed">
                Next
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </span>
            )}
          </nav>
        </div>
      )}
    </div>
  </section>

  <!-- Enhanced Newsletter Section -->
  <section class="py-20 bg-gradient-to-br from-ghibli-forest-600 via-ghibli-warm-600 to-ghibli-forest-700 text-white relative overflow-hidden">
    <!-- Ghibli-inspired decorative elements -->
    <div class="absolute inset-0 ghibli-stars opacity-30"></div>
    <div class="absolute top-0 left-0 w-full h-full ghibli-floating-orbs opacity-20"></div>

    <div class={`container mx-auto px-4 ${containerWidth} text-center relative z-10`}>
      <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl md:text-5xl font-ghibliHeading font-bold mb-6">
          Stay Informed with <span class="text-ghibli-warm-200">Expert Reviews</span>
        </h2>
        <p class="text-xl mb-12 text-ghibli-warm-100 leading-relaxed">
          Join our newsletter to receive the latest product reviews, buying guides, and exclusive discount codes from your favorite brands.
        </p>

        <div class="mb-12 bg-white/10 backdrop-blur-sm rounded-3xl p-8 max-w-2xl mx-auto border border-white/20">
          <form class="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Your email address"
              class="flex-1 px-6 py-4 rounded-2xl text-ghibli-forest-800 bg-white focus:outline-none focus:ring-2 focus:ring-ghibli-warm-300 placeholder-ghibli-earth-400"
              required
            />
            <button
              type="submit"
              class="bg-ghibli-warm-500 hover:bg-ghibli-warm-400 text-white font-medium px-8 py-4 rounded-2xl transition-all duration-300 hover:shadow-lg hover:scale-105"
            >
              Subscribe
            </button>
          </form>
        </div>

        <div class="border-t border-white/20 pt-12">
          <h3 class="text-2xl md:text-3xl font-ghibliHeading font-bold mb-6">
            Can't Find the Brand You're Looking For?
          </h3>
          <p class="text-lg mb-8 text-ghibli-warm-100 leading-relaxed">
            Tell us which brand you want to learn about, and we'll provide expert reviews and recommendations.
          </p>
          <a
            href="/contact"
            class="inline-flex items-center bg-white text-ghibli-forest-700 hover:bg-ghibli-warm-50 font-medium px-8 py-4 rounded-2xl transition-all duration-300 hover:shadow-lg hover:scale-105"
          >
            Contact Us
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </a>
        </div>
      </div>
    </div>
  </section>
</MainLayout>