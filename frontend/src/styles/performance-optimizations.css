/* 2025 Performance Optimizations for Ghibli Effects */
/* Ensuring smooth performance across all devices */

/* ===== GPU ACCELERATION ===== */

/* Force GPU acceleration for key animated elements */
.ghibli-particles-enhanced,
.ghibli-dynamic-bg,
.ghibli-float-physics,
.ghibli-glow-enhanced,
.ghibli-shimmer,
.ghibli-aurora,
.ghibli-fireflies,
.ghibli-wind,
.ghibli-crystal-atmosphere {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize animations for 60fps */
@media (min-width: 768px) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    animation-timing-function: linear;
    transform: translate3d(0, 0, 0);
  }
}

/* ===== RESPONSIVE PERFORMANCE ===== */

/* Mobile optimizations */
@media (max-width: 767px) {
  /* Reduce particle density on mobile */
  .ghibli-particles-enhanced::before {
    background-size: 100px 50px;
    animation-duration: 15s;
  }
  
  .ghibli-particles-enhanced::after {
    background-size: 150px 75px;
    animation-duration: 18s;
  }
  
  /* Simplify complex effects on mobile */
  .ghibli-fireflies::before,
  .ghibli-fireflies::after {
    display: none;
  }
  
  .ghibli-clouds::before,
  .ghibli-clouds::after {
    display: none;
  }
  
  /* Reduce blur effects for better performance */
  .ghibli-depth-blur::before {
    backdrop-filter: blur(0.25px);
  }
  
  .ghibli-crystal-atmosphere::before {
    filter: blur(0.5px);
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
  .ghibli-particles-enhanced::before {
    background-size: 150px 75px;
  }
  
  .ghibli-particles-enhanced::after {
    background-size: 200px 100px;
  }
}

/* ===== PERFORMANCE BASED ON DEVICE CAPABILITIES ===== */

/* Reduced effects for low-end devices */
.ghibli-reduced-effects .ghibli-particles-enhanced::before,
.ghibli-reduced-effects .ghibli-particles-enhanced::after,
.ghibli-reduced-effects .ghibli-fireflies::before,
.ghibli-reduced-effects .ghibli-fireflies::after,
.ghibli-reduced-effects .ghibli-clouds::before,
.ghibli-reduced-effects .ghibli-clouds::after {
  display: none;
}

.ghibli-reduced-effects .ghibli-aurora::before,
.ghibli-reduced-effects .ghibli-shimmer::after,
.ghibli-reduced-effects .ghibli-wind::before {
  animation-duration: 8s;
  opacity: 0.3;
}

/* ===== ACCESSIBILITY AND MOTION PREFERENCES ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after,
  .ghibli-wind::before,
  .ghibli-fireflies::before,
  .ghibli-fireflies::after,
  .ghibli-float-physics,
  .ghibli-dynamic-bg::before,
  .ghibli-crystal-atmosphere::before,
  .ghibli-rainbow-shimmer::after,
  .ghibli-watercolor::before,
  .ghibli-landscape::before,
  .ghibli-clouds::before,
  .ghibli-clouds::after,
  .ghibli-aurora::before,
  .ghibli-shimmer::after {
    animation: none !important;
    transform: none !important;
  }
  
  .ghibli-glow-enhanced::before {
    animation: none !important;
  }
  
  /* Keep essential transitions but make them instant */
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

/* ===== INTERSECTION OBSERVER OPTIMIZATIONS ===== */

/* Only animate visible elements */
.ghibli-visible .ghibli-particles-enhanced::before,
.ghibli-visible .ghibli-particles-enhanced::after {
  animation-play-state: running;
}

.ghibli-particles-enhanced::before,
.ghibli-particles-enhanced::after {
  animation-play-state: paused;
}

/* ===== BATTERY OPTIMIZATION ===== */

/* Reduce effects when battery is low (if supported) */
@media (max-resolution: 1dppx) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    animation-duration: 30s;
    opacity: 0.3;
  }
}

/* ===== MEMORY OPTIMIZATION ===== */

/* Limit the number of simultaneous animations */
.ghibli-animation-queue {
  animation-fill-mode: forwards;
  animation-iteration-count: 1;
}

/* Clean up after animations */
.ghibli-cleanup::before,
.ghibli-cleanup::after {
  content: none;
}

/* ===== SCROLL PERFORMANCE ===== */

/* Optimize scroll-triggered animations */
.ghibli-scroll-optimized {
  contain: layout style paint;
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* ===== FONT LOADING OPTIMIZATION ===== */

/* Ensure fonts load efficiently */
.font-ghibli-loading {
  font-display: swap;
}

/* Fallback fonts while custom fonts load */
.ghibli-font-fallback {
  font-family: system-ui, -apple-system, sans-serif;
}

/* ===== IMAGE OPTIMIZATION ===== */

/* Optimize images with effects */
.ghibli-img-enhanced {
  image-rendering: optimizeQuality;
  transform: translateZ(0);
}

/* ===== CRITICAL PERFORMANCE HINTS ===== */

/* Hint browser about upcoming animations */
.ghibli-will-animate {
  will-change: transform, opacity, filter;
}

/* Remove will-change after animation */
.ghibli-animation-complete {
  will-change: auto;
}

/* ===== DARK MODE OPTIMIZATIONS ===== */

@media (prefers-color-scheme: dark) {
  /* Reduce bright effects in dark mode */
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    opacity: 0.6;
  }
  
  .ghibli-fireflies::before,
  .ghibli-fireflies::after {
    opacity: 0.8;
  }
  
  .ghibli-glow-enhanced::before {
    opacity: 0.7;
  }
}

/* ===== HIGH CONTRAST MODE ===== */

@media (prefers-contrast: high) {
  /* Simplify effects for better contrast */
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after,
  .ghibli-shimmer::after,
  .ghibli-aurora::before {
    display: none;
  }
  
  .ghibli-glow-enhanced::before {
    opacity: 0.2;
  }
}

/* ===== PRINT OPTIMIZATIONS ===== */

@media print {
  /* Remove all animations and effects for print */
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after,
  .ghibli-wind::before,
  .ghibli-fireflies::before,
  .ghibli-fireflies::after,
  .ghibli-clouds::before,
  .ghibli-clouds::after,
  .ghibli-aurora::before,
  .ghibli-shimmer::after,
  .ghibli-rainbow-shimmer::after,
  .ghibli-watercolor::before,
  .ghibli-landscape::before,
  .ghibli-crystal-atmosphere::before,
  .ghibli-dynamic-bg::before,
  .ghibli-glow-enhanced::before {
    display: none !important;
  }
  
  /* Ensure text remains readable */
  .ghibli-text-glow {
    text-shadow: none !important;
  }
}

/* ===== PERFORMANCE MONITORING ===== */

/* Add performance markers for debugging */
.ghibli-perf-marker {
  --ghibli-start-time: 0;
  --ghibli-end-time: 0;
}

/* ===== CONTAINER QUERIES FOR ADAPTIVE EFFECTS ===== */

@container (max-width: 400px) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    background-size: 80px 40px;
  }
}

@container (min-width: 1200px) {
  .ghibli-particles-enhanced::before {
    background-size: 250px 125px;
  }
  
  .ghibli-particles-enhanced::after {
    background-size: 350px 175px;
  }
}

/* ===== LOADING STATE OPTIMIZATIONS ===== */

/* Skeleton loading for heavy effects */
.ghibli-loading-skeleton {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 25%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0.1) 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* ===== CRITICAL CSS INLINING ===== */

/* Mark critical styles for above-the-fold content */
.ghibli-critical {
  /* These styles should be inlined */
  contain: layout style;
}

/* ===== FINAL PERFORMANCE SAFEGUARDS ===== */

/* Emergency performance mode */
.ghibli-emergency-mode * {
  animation: none !important;
  transition: none !important;
  transform: none !important;
  filter: none !important;
  backdrop-filter: none !important;
}

.ghibli-emergency-mode::before,
.ghibli-emergency-mode::after {
  display: none !important;
}
