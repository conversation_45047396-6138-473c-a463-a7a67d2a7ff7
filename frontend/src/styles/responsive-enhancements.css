/* 2025 Ultra-Responsive Design Enhancements */
/* Perfect experience across all devices and screen sizes */

/* ===== MODERN RESPONSIVE BREAKPOINTS ===== */

/* Ultra-small devices (phones in portrait) */
@media (max-width: 374px) {
  .ghibli-text-glow {
    font-size: clamp(1.5rem, 4vw, 2rem);
    line-height: 1.2;
  }
  
  .ghibli-glow-enhanced {
    padding: 1rem;
    border-radius: 1rem;
  }
  
  .ghibli-category-filter {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    margin: 0.25rem;
  }
  
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    background-size: 60px 30px;
  }
}

/* Small devices (phones) */
@media (min-width: 375px) and (max-width: 767px) {
  .ghibli-text-glow {
    font-size: clamp(1.75rem, 5vw, 2.5rem);
  }
  
  .ghibli-main-card-enhanced {
    margin: 1rem;
    border-radius: 1.5rem;
  }
  
  .ghibli-secondary-card-enhanced-1,
  .ghibli-secondary-card-enhanced-2 {
    margin: 0.5rem;
    border-radius: 1rem;
  }
  
  .ghibli-float-physics {
    animation-duration: 8s;
  }
}

/* Medium devices (tablets) */
@media (min-width: 768px) and (max-width: 1023px) {
  .ghibli-text-glow {
    font-size: clamp(2rem, 4vw, 3rem);
  }
  
  .ghibli-hero-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
  
  .ghibli-category-filter {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .ghibli-particles-enhanced::before {
    background-size: 150px 75px;
  }
  
  .ghibli-particles-enhanced::after {
    background-size: 200px 100px;
  }
}

/* Large devices (desktops) */
@media (min-width: 1024px) and (max-width: 1439px) {
  .ghibli-text-glow {
    font-size: clamp(2.5rem, 3vw, 4rem);
  }
  
  .ghibli-hero-grid {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2rem;
  }
  
  .ghibli-particles-enhanced::before {
    background-size: 200px 100px;
  }
  
  .ghibli-particles-enhanced::after {
    background-size: 300px 150px;
  }
}

/* Extra large devices (large desktops) */
@media (min-width: 1440px) {
  .ghibli-text-glow {
    font-size: clamp(3rem, 2.5vw, 5rem);
  }
  
  .ghibli-hero-grid {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2.5rem;
  }
  
  .ghibli-particles-enhanced::before {
    background-size: 250px 125px;
  }
  
  .ghibli-particles-enhanced::after {
    background-size: 350px 175px;
  }
  
  .ghibli-float-physics {
    animation-duration: 15s;
  }
}

/* ===== CONTAINER QUERIES FOR COMPONENT-LEVEL RESPONSIVENESS ===== */

@container (max-width: 300px) {
  .ghibli-deal-card,
  .ghibli-article-card {
    padding: 0.75rem;
    border-radius: 1rem;
  }
  
  .ghibli-deal-card h3,
  .ghibli-article-card h3 {
    font-size: 1rem;
    line-height: 1.3;
  }
}

@container (min-width: 301px) and (max-width: 500px) {
  .ghibli-deal-card,
  .ghibli-article-card {
    padding: 1rem;
    border-radius: 1.25rem;
  }
  
  .ghibli-deal-card h3,
  .ghibli-article-card h3 {
    font-size: 1.125rem;
    line-height: 1.4;
  }
}

@container (min-width: 501px) {
  .ghibli-deal-card,
  .ghibli-article-card {
    padding: 1.5rem;
    border-radius: 1.5rem;
  }
  
  .ghibli-deal-card h3,
  .ghibli-article-card h3 {
    font-size: 1.25rem;
    line-height: 1.5;
  }
}

/* ===== ORIENTATION-BASED ADJUSTMENTS ===== */

@media (orientation: portrait) {
  .ghibli-hero-section {
    min-height: 100vh;
    padding: 2rem 1rem;
  }
  
  .ghibli-landscape::before {
    height: 30%;
  }
  
  .ghibli-clouds::before,
  .ghibli-clouds::after {
    animation-duration: 25s;
  }
}

@media (orientation: landscape) and (max-height: 600px) {
  .ghibli-hero-section {
    min-height: 100vh;
    padding: 1rem;
  }
  
  .ghibli-text-glow {
    font-size: clamp(1.5rem, 3vh, 2.5rem);
  }
  
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    animation-duration: 20s;
  }
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */

@media (hover: none) and (pointer: coarse) {
  /* Touch devices */
  .ghibli-glow-enhanced:hover {
    transform: scale(1.02);
  }
  
  .ghibli-category-filter:hover {
    transform: translateY(-2px) scale(1.03);
  }
  
  .ghibli-sparkle::before,
  .ghibli-sparkle::after {
    display: none; /* Remove hover-dependent effects */
  }
  
  /* Larger touch targets */
  .ghibli-category-filter,
  .ghibli-button-enhanced {
    min-height: 44px;
    min-width: 44px;
  }
}

/* ===== HIGH DPI DISPLAYS ===== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    background-size: 100px 50px;
  }
  
  .ghibli-img-enhanced {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

@media (-webkit-min-device-pixel-ratio: 3), (min-resolution: 288dpi) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    background-size: 80px 40px;
  }
}

/* ===== FOLDABLE DEVICES ===== */

@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  /* Likely foldable device in portrait */
  .ghibli-hero-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .ghibli-main-card-enhanced {
    max-width: 100%;
  }
}

/* ===== ULTRA-WIDE DISPLAYS ===== */

@media (min-aspect-ratio: 21/9) {
  .ghibli-hero-section {
    padding: 2rem 10%;
  }
  
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    background-size: 400px 200px;
  }
  
  .ghibli-clouds::before {
    width: 300px;
    animation-duration: 50s;
  }
  
  .ghibli-clouds::after {
    width: 250px;
    animation-duration: 45s;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

@media (prefers-reduced-motion: reduce) {
  .ghibli-float-physics,
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    animation: none;
  }
  
  .ghibli-glow-enhanced:hover {
    transform: none;
    transition: background-color 0.2s ease;
  }
}

@media (prefers-contrast: high) {
  .ghibli-glow-enhanced {
    border: 2px solid currentColor;
  }
  
  .ghibli-text-glow {
    text-shadow: none;
    font-weight: bold;
  }
  
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    opacity: 0.1;
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  .ghibli-particles-enhanced,
  .ghibli-dynamic-bg,
  .ghibli-float-physics,
  .ghibli-shimmer,
  .ghibli-aurora,
  .ghibli-fireflies,
  .ghibli-wind,
  .ghibli-clouds {
    display: none !important;
  }
  
  .ghibli-text-glow {
    color: black !important;
    text-shadow: none !important;
  }
  
  .ghibli-glow-enhanced {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

/* ===== DYNAMIC VIEWPORT UNITS ===== */

.ghibli-hero-section {
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height */
}

.ghibli-full-width {
  width: 100vw;
  width: 100dvw; /* Dynamic viewport width */
}

/* ===== GRID AND FLEXBOX ENHANCEMENTS ===== */

.ghibli-responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
  gap: clamp(1rem, 3vw, 2rem);
}

.ghibli-responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(0.5rem, 2vw, 1.5rem);
}

/* ===== TYPOGRAPHY SCALING ===== */

.ghibli-responsive-text {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
  line-height: clamp(1.4, 1.5vw + 1rem, 1.8);
}

.ghibli-responsive-heading {
  font-size: clamp(1.5rem, 5vw, 4rem);
  line-height: clamp(1.1, 1vw + 1rem, 1.3);
}

/* ===== SPACING SYSTEM ===== */

.ghibli-responsive-padding {
  padding: clamp(1rem, 4vw, 3rem);
}

.ghibli-responsive-margin {
  margin: clamp(0.5rem, 2vw, 2rem);
}

/* ===== COMPONENT SIZING ===== */

.ghibli-responsive-card {
  width: clamp(280px, 90vw, 400px);
  height: clamp(200px, 50vh, 300px);
}

.ghibli-responsive-button {
  padding: clamp(0.5rem, 2vw, 1rem) clamp(1rem, 4vw, 2rem);
  font-size: clamp(0.875rem, 2vw, 1.125rem);
}

/* ===== SAFE AREA INSETS FOR MOBILE ===== */

.ghibli-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* ===== PERFORMANCE OPTIMIZATIONS BY SCREEN SIZE ===== */

@media (max-width: 767px) {
  .ghibli-performance-mobile {
    will-change: auto;
    transform: none;
  }
  
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    animation-duration: 20s;
    opacity: 0.3;
  }
}

@media (min-width: 1440px) {
  .ghibli-performance-desktop {
    will-change: transform, opacity;
  }
  
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    animation-duration: 12s;
    opacity: 0.6;
  }
}
