/**
 * 2025 Ultra-Advanced Ghibli Effects Testing and Validation Suite
 * Comprehensive testing for all visual effects, performance, and functionality
 */

class GhibliTesting2025 {
  constructor() {
    this.testResults = [];
    this.performanceMetrics = {};
    this.isTestingMode = false;
    
    this.init();
  }
  
  init() {
    // Only run tests in development or when explicitly requested
    if (window.location.search.includes('ghibli-test') || window.location.hostname === 'localhost') {
      this.runAllTests();
    }
  }
  
  async runAllTests() {
    console.log('🧪 Starting Ghibli Effects 2025 Testing Suite...');
    this.isTestingMode = true;
    
    try {
      await this.testVisualEffects();
      await this.testPerformance();
      await this.testResponsiveness();
      await this.testAccessibility();
      await this.testAPIIntegration();
      await this.testInteractivity();
      
      this.generateTestReport();
    } catch (error) {
      console.error('❌ Testing suite failed:', error);
    }
  }
  
  async testVisualEffects() {
    console.log('🎨 Testing Visual Effects...');
    
    const effects = [
      'ghibli-particles-enhanced',
      'ghibli-dynamic-bg',
      'ghibli-aurora',
      'ghibli-shimmer',
      'ghibli-fireflies',
      'ghibli-wind',
      'ghibli-clouds',
      'ghibli-crystal-atmosphere',
      'ghibli-watercolor',
      'ghibli-glow-enhanced'
    ];
    
    for (const effect of effects) {
      const elements = document.querySelectorAll(`.${effect}`);
      const result = {
        effect,
        elementsFound: elements.length,
        animationsActive: this.checkAnimations(elements),
        cssLoaded: this.checkCSSRules(effect)
      };
      
      this.testResults.push({
        category: 'Visual Effects',
        test: effect,
        passed: result.elementsFound > 0 && result.cssLoaded,
        details: result
      });
    }
  }
  
  async testPerformance() {
    console.log('⚡ Testing Performance...');
    
    const startTime = performance.now();
    
    // Test animation frame rate
    const frameRateTest = await this.measureFrameRate();
    
    // Test memory usage (if available)
    const memoryTest = this.measureMemoryUsage();
    
    // Test load times
    const loadTimeTest = this.measureLoadTimes();
    
    // Test scroll performance
    const scrollTest = await this.testScrollPerformance();
    
    const endTime = performance.now();
    
    this.performanceMetrics = {
      frameRate: frameRateTest,
      memory: memoryTest,
      loadTime: loadTimeTest,
      scrollPerformance: scrollTest,
      testDuration: endTime - startTime
    };
    
    this.testResults.push({
      category: 'Performance',
      test: 'Overall Performance',
      passed: frameRateTest.averageFPS > 30 && scrollTest.averageFrameTime < 16.67,
      details: this.performanceMetrics
    });
  }
  
  async testResponsiveness() {
    console.log('📱 Testing Responsiveness...');
    
    const breakpoints = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1440, height: 900 },
      { name: 'Ultra-wide', width: 2560, height: 1440 }
    ];
    
    for (const breakpoint of breakpoints) {
      // Simulate viewport change
      const mediaQuery = `(max-width: ${breakpoint.width}px)`;
      const matches = window.matchMedia(mediaQuery).matches;
      
      // Test responsive classes
      const responsiveElements = document.querySelectorAll('[class*="ghibli-responsive"]');
      
      this.testResults.push({
        category: 'Responsiveness',
        test: `${breakpoint.name} Breakpoint`,
        passed: responsiveElements.length > 0,
        details: {
          breakpoint,
          mediaQueryMatches: matches,
          responsiveElementsFound: responsiveElements.length
        }
      });
    }
  }
  
  async testAccessibility() {
    console.log('♿ Testing Accessibility...');
    
    // Test reduced motion preference
    const reducedMotionTest = this.testReducedMotion();
    
    // Test high contrast mode
    const highContrastTest = this.testHighContrast();
    
    // Test keyboard navigation
    const keyboardTest = this.testKeyboardNavigation();
    
    // Test ARIA labels
    const ariaTest = this.testARIALabels();
    
    // Test color contrast
    const contrastTest = this.testColorContrast();
    
    this.testResults.push({
      category: 'Accessibility',
      test: 'Accessibility Compliance',
      passed: reducedMotionTest && highContrastTest && keyboardTest && ariaTest,
      details: {
        reducedMotion: reducedMotionTest,
        highContrast: highContrastTest,
        keyboard: keyboardTest,
        aria: ariaTest,
        contrast: contrastTest
      }
    });
  }
  
  async testAPIIntegration() {
    console.log('🔌 Testing API Integration...');
    
    // Test if API calls are working
    const apiEndpoints = [
      '/api/v1/article',
      '/api/v1/brand',
      '/api/v1/category',
      '/api/v1/deal',
      '/api/v1/tag'
    ];
    
    const apiResults = [];
    
    for (const endpoint of apiEndpoints) {
      try {
        const response = await fetch(`http://localhost:8080${endpoint}?page_size=1`);
        apiResults.push({
          endpoint,
          status: response.status,
          ok: response.ok,
          responseTime: performance.now()
        });
      } catch (error) {
        apiResults.push({
          endpoint,
          status: 'error',
          ok: false,
          error: error.message
        });
      }
    }
    
    const allAPIsWorking = apiResults.every(result => result.ok);
    
    this.testResults.push({
      category: 'API Integration',
      test: 'API Endpoints',
      passed: allAPIsWorking,
      details: apiResults
    });
  }
  
  async testInteractivity() {
    console.log('🖱️ Testing Interactivity...');
    
    // Test hover effects
    const hoverTest = this.testHoverEffects();
    
    // Test click interactions
    const clickTest = this.testClickInteractions();
    
    // Test scroll animations
    const scrollAnimationTest = this.testScrollAnimations();
    
    // Test form interactions
    const formTest = this.testFormInteractions();
    
    this.testResults.push({
      category: 'Interactivity',
      test: 'User Interactions',
      passed: hoverTest && clickTest && scrollAnimationTest,
      details: {
        hover: hoverTest,
        click: clickTest,
        scrollAnimation: scrollAnimationTest,
        forms: formTest
      }
    });
  }
  
  // Helper methods
  checkAnimations(elements) {
    return Array.from(elements).some(el => {
      const computedStyle = window.getComputedStyle(el);
      return computedStyle.animationName !== 'none' || 
             computedStyle.transform !== 'none' ||
             computedStyle.transition !== 'none';
    });
  }
  
  checkCSSRules(className) {
    const styleSheets = Array.from(document.styleSheets);
    return styleSheets.some(sheet => {
      try {
        const rules = Array.from(sheet.cssRules || sheet.rules || []);
        return rules.some(rule => 
          rule.selectorText && rule.selectorText.includes(className)
        );
      } catch (e) {
        return false; // Cross-origin stylesheets
      }
    });
  }
  
  async measureFrameRate() {
    return new Promise(resolve => {
      let frames = 0;
      let lastTime = performance.now();
      const duration = 1000; // 1 second
      
      function countFrames() {
        frames++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= duration) {
          resolve({
            averageFPS: frames,
            duration: currentTime - lastTime
          });
        } else {
          requestAnimationFrame(countFrames);
        }
      }
      
      requestAnimationFrame(countFrames);
    });
  }
  
  measureMemoryUsage() {
    if ('memory' in performance) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return { available: false };
  }
  
  measureLoadTimes() {
    const navigation = performance.getEntriesByType('navigation')[0];
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      totalTime: navigation.loadEventEnd - navigation.navigationStart
    };
  }
  
  async testScrollPerformance() {
    return new Promise(resolve => {
      let frameCount = 0;
      let totalFrameTime = 0;
      let lastFrameTime = performance.now();
      
      function measureFrame() {
        const currentTime = performance.now();
        const frameTime = currentTime - lastFrameTime;
        totalFrameTime += frameTime;
        frameCount++;
        lastFrameTime = currentTime;
        
        if (frameCount < 60) {
          requestAnimationFrame(measureFrame);
        } else {
          resolve({
            averageFrameTime: totalFrameTime / frameCount,
            frameCount
          });
        }
      }
      
      // Trigger scroll to test performance
      window.scrollBy(0, 100);
      requestAnimationFrame(measureFrame);
    });
  }
  
  testReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    const hasReducedMotionClass = document.documentElement.classList.contains('ghibli-no-motion');
    
    return !prefersReducedMotion || hasReducedMotionClass;
  }
  
  testHighContrast() {
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    const hasHighContrastStyles = this.checkCSSRules('prefers-contrast');
    
    return !prefersHighContrast || hasHighContrastStyles;
  }
  
  testKeyboardNavigation() {
    const focusableElements = document.querySelectorAll('a, button, input, textarea, select, [tabindex]');
    return focusableElements.length > 0;
  }
  
  testARIALabels() {
    const interactiveElements = document.querySelectorAll('button, a, input');
    let hasProperLabels = true;
    
    interactiveElements.forEach(el => {
      if (!el.getAttribute('aria-label') && !el.textContent.trim() && !el.getAttribute('title')) {
        hasProperLabels = false;
      }
    });
    
    return hasProperLabels;
  }
  
  testColorContrast() {
    // Basic color contrast test (simplified)
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a');
    return textElements.length > 0; // Simplified test
  }
  
  testHoverEffects() {
    const hoverElements = document.querySelectorAll('[data-ghibli-interactive], .ghibli-glow-enhanced');
    return hoverElements.length > 0;
  }
  
  testClickInteractions() {
    const clickableElements = document.querySelectorAll('a, button, [onclick]');
    return clickableElements.length > 0;
  }
  
  testScrollAnimations() {
    const scrollElements = document.querySelectorAll('[data-ghibli-animate]');
    return scrollElements.length > 0;
  }
  
  testFormInteractions() {
    const forms = document.querySelectorAll('form');
    const inputs = document.querySelectorAll('input, textarea, select');
    return forms.length > 0 || inputs.length > 0;
  }
  
  generateTestReport() {
    const passedTests = this.testResults.filter(test => test.passed).length;
    const totalTests = this.testResults.length;
    const passRate = (passedTests / totalTests * 100).toFixed(1);
    
    console.log(`
🎉 Ghibli Effects 2025 Testing Complete!
📊 Test Results: ${passedTests}/${totalTests} passed (${passRate}%)
⚡ Performance Metrics:`, this.performanceMetrics);
    
    console.table(this.testResults);
    
    // Create visual test report
    this.createVisualReport(passedTests, totalTests, passRate);
  }
  
  createVisualReport(passed, total, passRate) {
    const reportElement = document.createElement('div');
    reportElement.id = 'ghibli-test-report';
    reportElement.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 2px solid #8e9b5e;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-family: 'Inter', sans-serif;
        max-width: 300px;
      ">
        <h3 style="margin: 0 0 15px 0; color: #8e9b5e; font-size: 18px;">
          🧪 Ghibli Test Report
        </h3>
        <div style="margin-bottom: 10px;">
          <strong>Tests Passed:</strong> ${passed}/${total}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Pass Rate:</strong> ${passRate}%
        </div>
        <div style="
          width: 100%;
          height: 10px;
          background: #e0e0e0;
          border-radius: 5px;
          overflow: hidden;
          margin-bottom: 15px;
        ">
          <div style="
            width: ${passRate}%;
            height: 100%;
            background: linear-gradient(90deg, #8e9b5e, #b1b479);
            transition: width 0.5s ease;
          "></div>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #8e9b5e;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 8px;
          cursor: pointer;
          font-size: 14px;
        ">Close</button>
      </div>
    `;
    
    document.body.appendChild(reportElement);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (reportElement.parentElement) {
        reportElement.remove();
      }
    }, 10000);
  }
}

// Auto-initialize testing suite
if (typeof window !== 'undefined') {
  window.ghibliTesting = new GhibliTesting2025();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GhibliTesting2025;
}
