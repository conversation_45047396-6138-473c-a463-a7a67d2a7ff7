/**
 * 2025 Ultra-Advanced Studio Ghibli Interactive Effects Library
 * The most sophisticated and magical interactive effects inspired by Studio Ghibli
 */

class GhibliEffects2025 {
  constructor() {
    this.mouseX = 0;
    this.mouseY = 0;
    this.particles = [];
    this.sparkles = [];
    this.isInitialized = false;
    
    this.init();
  }
  
  init() {
    if (this.isInitialized) return;
    
    this.setupMouseTracking();
    this.setupScrollAnimations();
    this.setupMagicalInteractions();
    this.setupParticleSystem();
    this.setupPerformanceOptimizations();
    
    this.isInitialized = true;
    console.log('🌟 Ghibli Effects 2025 initialized successfully!');
  }
  
  setupMouseTracking() {
    let ticking = false;
    
    document.addEventListener('mousemove', (e) => {
      this.mouseX = e.clientX;
      this.mouseY = e.clientY;
      
      if (!ticking) {
        requestAnimationFrame(() => {
          this.updateMouseEffects();
          ticking = false;
        });
        ticking = true;
      }
    });
  }
  
  updateMouseEffects() {
    // Update CSS custom properties for mouse-following effects
    document.documentElement.style.setProperty('--mouse-x', `${(this.mouseX / window.innerWidth) * 100}%`);
    document.documentElement.style.setProperty('--mouse-y', `${(this.mouseY / window.innerHeight) * 100}%`);
    
    // Create magical sparkles on mouse move (reduced frequency for performance)
    if (Math.random() > 0.98) {
      this.createSparkle(this.mouseX, this.mouseY);
    }
  }
  
  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    const scrollObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateElement(entry.target);
        }
      });
    }, observerOptions);
    
    // Observe elements with animation attributes
    const animatedElements = document.querySelectorAll('[data-ghibli-animate]');
    animatedElements.forEach(element => {
      scrollObserver.observe(element);
    });
    
    // Auto-detect common elements for animation
    const autoAnimateSelectors = [
      '[data-article-index]',
      '[data-deal-index]',
      '[data-brand-index]',
      '[data-brand-grid-index]',
      '.ghibli-glow-enhanced',
      'article',
      '.card'
    ];
    
    autoAnimateSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (!element.hasAttribute('data-ghibli-animate')) {
          element.setAttribute('data-ghibli-animate', 'fadeInUp');
          scrollObserver.observe(element);
        }
      });
    });
  }
  
  animateElement(element) {
    const animationType = element.getAttribute('data-ghibli-animate') || 'fadeInUp';
    
    element.classList.add('animate-in');
    
    // Add magical entrance effect
    switch (animationType) {
      case 'fadeInUp':
        this.addMagicalEntrance(element);
        break;
      case 'sparkleIn':
        this.addSparkleEntrance(element);
        break;
      case 'glowIn':
        this.addGlowEntrance(element);
        break;
      default:
        this.addMagicalEntrance(element);
    }
  }
  
  addMagicalEntrance(element) {
    const sparkleEffect = document.createElement('div');
    sparkleEffect.innerHTML = '✨';
    sparkleEffect.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 20px;
      animation: sparkleAppear 2s ease-out forwards;
      pointer-events: none;
      z-index: 100;
    `;
    
    element.style.position = 'relative';
    element.appendChild(sparkleEffect);
    
    setTimeout(() => {
      sparkleEffect.remove();
    }, 2000);
  }
  
  addSparkleEntrance(element) {
    const sparkles = ['✨', '🌟', '💫', '⭐'];
    const sparkleCount = 3;
    
    for (let i = 0; i < sparkleCount; i++) {
      setTimeout(() => {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = sparkles[Math.floor(Math.random() * sparkles.length)];
        sparkle.style.cssText = `
          position: absolute;
          top: ${Math.random() * 100}%;
          left: ${Math.random() * 100}%;
          font-size: ${12 + Math.random() * 8}px;
          animation: sparkleExplosion 1.5s ease-out forwards;
          pointer-events: none;
          z-index: 100;
        `;
        
        element.style.position = 'relative';
        element.appendChild(sparkle);
        
        setTimeout(() => {
          sparkle.remove();
        }, 1500);
      }, i * 200);
    }
  }
  
  addGlowEntrance(element) {
    element.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
    element.style.boxShadow = '0 0 30px rgba(255, 193, 7, 0.6)';
    
    setTimeout(() => {
      element.style.boxShadow = '';
    }, 2000);
  }
  
  setupMagicalInteractions() {
    // Enhanced hover effects for interactive elements
    const interactiveElements = document.querySelectorAll('a, button, .ghibli-glow-enhanced, [data-ghibli-interactive]');
    
    interactiveElements.forEach(element => {
      element.addEventListener('mouseenter', () => {
        this.addHoverMagic(element);
      });
      
      element.addEventListener('mouseleave', () => {
        this.removeHoverMagic(element);
      });
      
      element.addEventListener('click', () => {
        this.addClickMagic(element);
      });
    });
  }
  
  addHoverMagic(element) {
    // Add magical glow effect
    element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    element.style.filter = 'brightness(1.1) saturate(1.2)';
    
    // Create hover particles
    if (Math.random() > 0.7) {
      this.createHoverParticles(element);
    }
  }
  
  removeHoverMagic(element) {
    element.style.filter = '';
  }
  
  addClickMagic(element) {
    // Create click explosion effect
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    this.createClickExplosion(centerX, centerY);
  }
  
  createSparkle(x, y) {
    const sparkle = document.createElement('div');
    const sparkleTypes = ['✨', '🌟', '💫'];
    sparkle.innerHTML = sparkleTypes[Math.floor(Math.random() * sparkleTypes.length)];
    sparkle.style.cssText = `
      position: fixed;
      left: ${x}px;
      top: ${y}px;
      font-size: ${8 + Math.random() * 6}px;
      pointer-events: none;
      z-index: 9999;
      animation: sparkleTrail 1s ease-out forwards;
    `;
    
    document.body.appendChild(sparkle);
    
    setTimeout(() => {
      sparkle.remove();
    }, 1000);
  }
  
  createHoverParticles(element) {
    const rect = element.getBoundingClientRect();
    const particles = ['✨', '🌟', '💫'];
    
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        const particle = document.createElement('div');
        particle.innerHTML = particles[Math.floor(Math.random() * particles.length)];
        particle.style.cssText = `
          position: fixed;
          left: ${rect.left + Math.random() * rect.width}px;
          top: ${rect.top + Math.random() * rect.height}px;
          font-size: ${10 + Math.random() * 4}px;
          pointer-events: none;
          z-index: 9999;
          animation: particleRise 1.5s ease-out forwards;
        `;
        
        document.body.appendChild(particle);
        
        setTimeout(() => {
          particle.remove();
        }, 1500);
      }, i * 100);
    }
  }
  
  createClickExplosion(x, y) {
    const particles = ['✨', '🌟', '💫', '⭐', '🎆'];
    const particleCount = 8;
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.innerHTML = particles[Math.floor(Math.random() * particles.length)];
      
      const angle = (i / particleCount) * Math.PI * 2;
      const velocity = 50 + Math.random() * 30;
      const endX = x + Math.cos(angle) * velocity;
      const endY = y + Math.sin(angle) * velocity;
      
      particle.style.cssText = `
        position: fixed;
        left: ${x}px;
        top: ${y}px;
        font-size: ${12 + Math.random() * 8}px;
        pointer-events: none;
        z-index: 9999;
        animation: explosionParticle 1s ease-out forwards;
        --end-x: ${endX}px;
        --end-y: ${endY}px;
      `;
      
      document.body.appendChild(particle);
      
      setTimeout(() => {
        particle.remove();
      }, 1000);
    }
  }
  
  setupParticleSystem() {
    // Advanced particle system for ambient effects
    this.createAmbientParticles();
    
    // Refresh particles periodically
    setInterval(() => {
      this.createAmbientParticles();
    }, 10000);
  }
  
  createAmbientParticles() {
    if (this.particles.length > 20) return; // Limit particles for performance
    
    const particle = {
      x: Math.random() * window.innerWidth,
      y: window.innerHeight + 10,
      vx: (Math.random() - 0.5) * 2,
      vy: -1 - Math.random() * 2,
      life: 1,
      decay: 0.005 + Math.random() * 0.01
    };
    
    this.particles.push(particle);
  }
  
  setupPerformanceOptimizations() {
    // Use Intersection Observer for performance
    const performanceObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const element = entry.target;
        if (entry.isIntersecting) {
          element.classList.add('ghibli-visible');
        } else {
          element.classList.remove('ghibli-visible');
        }
      });
    });
    
    // Observe elements with heavy effects
    const heavyEffectElements = document.querySelectorAll('.ghibli-particles-enhanced, .ghibli-dynamic-bg');
    heavyEffectElements.forEach(element => {
      performanceObserver.observe(element);
    });
    
    // Reduce effects on low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
      document.documentElement.classList.add('ghibli-reduced-effects');
    }
    
    // Respect user preferences
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      document.documentElement.classList.add('ghibli-no-motion');
    }
  }
  
  // Public API methods
  addCustomAnimation(element, animationType = 'fadeInUp') {
    element.setAttribute('data-ghibli-animate', animationType);
    this.animateElement(element);
  }
  
  createCustomSparkle(x, y, type = '✨') {
    this.createSparkle(x, y);
  }
  
  destroy() {
    // Cleanup method
    this.particles = [];
    this.sparkles = [];
    this.isInitialized = false;
  }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.ghibliEffects = new GhibliEffects2025();
});

// Add dynamic CSS animations
const style = document.createElement('style');
style.textContent = `
  @keyframes sparkleAppear {
    0% { transform: scale(0) rotate(0deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
    100% { transform: scale(0) rotate(360deg); opacity: 0; }
  }
  
  @keyframes sparkleTrail {
    0% { transform: scale(1) translateY(0); opacity: 1; }
    100% { transform: scale(0) translateY(-20px); opacity: 0; }
  }
  
  @keyframes sparkleExplosion {
    0% { transform: scale(0) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.5) rotate(180deg); opacity: 0.8; }
    100% { transform: scale(0) rotate(360deg); opacity: 0; }
  }
  
  @keyframes particleRise {
    0% { transform: translateY(0) scale(1); opacity: 1; }
    100% { transform: translateY(-30px) scale(0); opacity: 0; }
  }
  
  @keyframes explosionParticle {
    0% { transform: translate(0, 0) scale(1); opacity: 1; }
    100% { transform: translate(var(--end-x, 0), var(--end-y, 0)) scale(0); opacity: 0; }
  }
  
  /* Reduced effects for performance */
  .ghibli-reduced-effects .ghibli-particles-enhanced::before,
  .ghibli-reduced-effects .ghibli-particles-enhanced::after {
    display: none;
  }
  
  .ghibli-no-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
`;
document.head.appendChild(style);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GhibliEffects2025;
}
