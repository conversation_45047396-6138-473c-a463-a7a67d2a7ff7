import"./hoisted.3b8a7569.js";document.addEventListener("DOMContentLoaded",()=>{const d=document.getElementById("carousel-track"),i=document.querySelectorAll(".carousel-slide"),m=document.querySelectorAll(".carousel-indicator"),v=document.getElementById("carousel-prev"),g=document.getElementById("carousel-next"),l=document.getElementById("carousel-container");if(!d||i.length===0||!v||!g||!l){console.error("Some carousel elements are missing");return}let e=0;const s=i.length;let r,a=!1;function n(u=!1){a&&!u||(a=!0,d.style.transform=`translateX(-${e*100}%)`,m.forEach((t,f)=>{f===e?(t.classList.remove("bg-gray-300"),t.classList.add("bg-purple-600"),t.classList.add("scale-110")):(t.classList.remove("bg-purple-600"),t.classList.add("bg-gray-300"),t.classList.remove("scale-110"))}),setTimeout(()=>{a=!1},700))}v.addEventListener("click",()=>{e=(e-1+s)%s,n(),c()}),g.addEventListener("click",()=>{e=(e+1)%s,n(),c()}),m.forEach((u,t)=>{u.addEventListener("click",()=>{e!==t&&(e=t,n(),c())})});function o(){r=setInterval(()=>{e=(e+1)%s,n()},6e3)}function c(){clearInterval(r),o()}l.addEventListener("mouseenter",()=>{clearInterval(r)}),l.addEventListener("mouseleave",()=>{o()}),o()});
