import"./hoisted.3b8a7569.js";document.addEventListener("DOMContentLoaded",()=>{const r=document.querySelectorAll('[href^="#"]');r.forEach(e=>{e.addEventListener("click",s=>{if(s.preventDefault(),r.forEach(t=>{t.classList.remove("bg-purple-600","text-white"),t.classList.add("bg-gray-100","text-gray-700")}),e){e.classList.remove("bg-gray-100","text-gray-700"),e.classList.add("bg-purple-600","text-white");const t=e.getAttribute("href")?.substring(1)||"all";console.log(`Filter by: ${t}`)}})})});
