import"./hoisted.3b8a7569.js";document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("brandSearch");if(e){const i=["Apple","Samsung","Sony","Asus","LG","Google","Xiaomi","Huawei","Dell","Logitech","Amazon","Microsoft","Razer","HP","Acer","Lenovo","Canon","Nikon","Intel","AMD"],o=e.closest(".card");if(!o)return;const t=o.querySelector(".space-y-1");if(!t)return;const l=t.innerHTML,s=e.parentElement;if(!s)return;const c=s.querySelector("button");if(!c)return;e.addEventListener("input",()=>{const r=e.value.toLowerCase();if(!r){t.innerHTML=l;return}const d=i.filter(n=>n.toLowerCase().includes(r));if(d.length>0){let n=`
            <a 
              href="?brand=" 
              class="block w-full text-left px-3 py-2 rounded-md text-sm text-gray-700 hover:bg-gray-100"
            >
              All Brands
            </a>
          `;d.forEach(a=>{const u=new URLSearchParams(window.location.search).get("brand")===a;n+=`
              <a 
                href="?brand=${encodeURIComponent(a)}" 
                class="block w-full text-left px-3 py-2 rounded-md text-sm ${u?"bg-purple-100 text-purple-800 font-medium":"text-gray-700 hover:bg-gray-100"}"
              >
                ${a}
              </a>
            `}),t.innerHTML=n}else t.innerHTML=`
            <p class="text-gray-500 text-sm px-3 py-2">No brands found matching "${r}"</p>
            <a 
              href="?brand=" 
              class="block w-full text-left px-3 py-2 rounded-md text-sm text-gray-700 hover:bg-gray-100"
            >
              All Brands
            </a>
          `}),c.addEventListener("click",()=>{e.value="",t.innerHTML=l})}});
