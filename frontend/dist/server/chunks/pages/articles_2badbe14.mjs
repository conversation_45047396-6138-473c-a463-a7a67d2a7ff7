/* empty css                           */import { g as create<PERSON>tro, e as createComponent, r as renderTemplate, j as renderComponent, m as maybeRenderHead, f as addAttribute } from '../astro_0d22710d.mjs';
import { $ as $$MainLayout } from './_slug__fe545221.mjs';
import * as fs from 'fs';
import * as path from 'path';

const API_TIMEOUT = 1e4;
const API_VERSION = "v1";
const getEnvironment = () => {
  try {
    const envPath = path.resolve(process.cwd(), "env");
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, "utf8").trim();
      const match = envContent.match(/env=(\w+)/);
      if (match && match[1]) {
        return match[1];
      }
    }
  } catch (error) {
    console.warn("无法读取env文件，使用默认环境", error);
  }
  return "local";
};
const getApiBaseUrl = () => {
  const env = getEnvironment();
  if (env === "local") {
    return "http://127.0.0.1:9091";
  } else if (env === "live") {
    return "https://smartreviews.top";
  }
  return "http://127.0.0.1:9091";
};
const handleApiError = (error) => {
  console.error("API Error:", error);
  let errorMessage = "Unknown error occurred";
  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === "string") {
    errorMessage = error;
  } else if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  }
  throw new Error(`API request failed: ${errorMessage}`);
};
async function fetchWithTimeout(url, options = {}, timeout = API_TIMEOUT) {
  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);
  try {
    console.log(`Sending request to: ${url}`);
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(id);
    return response;
  } catch (error) {
    clearTimeout(id);
    if (error.name === "AbortError") {
      throw new Error(`Request timed out after ${timeout}ms`);
    }
    throw error;
  }
}
const CategoryApi = {
  /**
   * Get list of categories from the backend
   */
  async getCategoryList(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      const page = params.page || 1;
      const pageSize = params.page_size || 30;
      queryParams.append("page", page.toString());
      queryParams.append("page_size", pageSize.toString());
      if (params.featured !== void 0)
        queryParams.append("featured", params.featured.toString());
      if (params.search)
        queryParams.append("search", params.search);
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : "";
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/category${queryString}`;
      console.log(`Fetching categories from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      const response = await fetchWithTimeout(url);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch categories: ${response.status} ${response.statusText} - ${errorText}`);
      }
      const apiResponse = await response.json();
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  /**
   * Get a specific category by slug
   * Note: This API method is not implemented yet on the backend
   */
  async getCategoryBySlug(slug) {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/category/${slug}`;
      const response = await fetchWithTimeout(url);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch category details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      const apiResponse = await response.json();
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};
const TagApi = {
  /**
   * Get list of tags from the backend
   */
  async getTagList(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      const page = params.page || 1;
      const pageSize = params.page_size || 50;
      queryParams.append("page", page.toString());
      queryParams.append("page_size", pageSize.toString());
      if (params.featured !== void 0)
        queryParams.append("featured", params.featured.toString());
      if (params.search)
        queryParams.append("search", params.search);
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : "";
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/tag${queryString}`;
      console.log(`Fetching tags from: ${url}`);
      console.log(`With params:`, JSON.stringify(params));
      const response = await fetchWithTimeout(url);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch tags: ${response.status} ${response.statusText} - ${errorText}`);
      }
      const apiResponse = await response.json();
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      console.log(`API response success. Received page ${apiResponse.data.page} of ${Math.ceil(apiResponse.data.total / apiResponse.data.page_size)}, total: ${apiResponse.data.total}`);
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  },
  /**
   * Get a specific tag by slug
   * Note: This API method is not implemented yet on the backend
   */
  async getTagBySlug(slug) {
    try {
      const url = `${getApiBaseUrl()}/api/${API_VERSION}/tag/${slug}`;
      const response = await fetchWithTimeout(url);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch tag details: ${response.status} ${response.statusText} - ${errorText}`);
      }
      const apiResponse = await response.json();
      if (apiResponse.code !== 200) {
        throw new Error(`API returned error: ${apiResponse.message}`);
      }
      return apiResponse.data;
    } catch (error) {
      return handleApiError(error);
    }
  }
};
const Api = {
  Tag: TagApi,
  Category: CategoryApi
  // Other API modules can be added here as the application grows
};

const $$Astro = createAstro("https://smartreviews.com");
const $$Articles = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Articles;
  const allArticles = [
    {
      id: 1,
      title: "Top 10 Wireless Headphones for 2023",
      excerpt: "Discover the best wireless headphones that combine superior sound quality, comfort, and battery life for an exceptional listening experience.",
      image: "/images/headphones.jpg",
      category: "Audio",
      brand: "Sony",
      publishedDate: "October 15, 2023",
      author: "John Smith",
      slug: "top-10-wireless-headphones-2023",
      tags: ["Wireless", "Audio", "Bluetooth"]
    },
    {
      id: 2,
      title: "The Ultimate Guide to Choosing a Gaming Laptop",
      excerpt: "Everything you need to know about selecting the perfect gaming laptop that meets your performance needs and budget constraints.",
      image: "/images/gaming-laptop.jpg",
      category: "Computers",
      brand: "Asus",
      publishedDate: "October 10, 2023",
      author: "Emily Johnson",
      slug: "ultimate-guide-gaming-laptop",
      tags: ["Gaming", "Laptops", "Budget"]
    },
    {
      id: 3,
      title: "Smart Home Devices That Actually Save You Money",
      excerpt: "These smart home gadgets not only make your life more convenient but also help reduce your monthly bills significantly.",
      image: "/images/smart-home.jpg",
      category: "Smart Home",
      brand: "Amazon",
      publishedDate: "October 5, 2023",
      author: "Michael Brown",
      slug: "smart-home-devices-save-money",
      tags: ["Smart Home", "Budget", "Productivity"]
    },
    {
      id: 4,
      title: "Best Budget Smartphones Under $300",
      excerpt: "Quality smartphones don't have to break the bank. Here are our top picks for budget-friendly options in 2023.",
      image: "/images/budget-smartphone.jpg",
      category: "Smartphones",
      brand: "Xiaomi",
      publishedDate: "October 1, 2023",
      author: "Sarah Wilson",
      slug: "best-budget-smartphones-under-300",
      tags: ["Smartphones", "Budget", "Portable"]
    },
    {
      id: 5,
      title: "Mechanical Keyboards: Are They Worth the Hype?",
      excerpt: "We dive deep into the world of mechanical keyboards to help you decide if they're worth the investment for your typing needs.",
      image: "/images/mechanical-keyboard.jpg",
      category: "Peripherals",
      brand: "Logitech",
      publishedDate: "September 28, 2023",
      author: "David Lee",
      slug: "mechanical-keyboards-worth-the-hype",
      tags: ["Mechanical", "Peripherals", "Premium"]
    },
    {
      id: 6,
      title: "The Best Fitness Trackers for Every Budget",
      excerpt: "From basic step counters to advanced health monitors, find the perfect fitness tracker to help you achieve your wellness goals.",
      image: "/images/fitness-trackers.jpg",
      category: "Wearables",
      brand: "Apple",
      publishedDate: "September 25, 2023",
      author: "Jennifer Martinez",
      slug: "best-fitness-trackers-every-budget",
      tags: ["Wearable", "Fitness", "Waterproof", "Bluetooth"]
    },
    {
      id: 7,
      title: "How to Choose the Right 4K TV in 2023",
      excerpt: "Navigate the complex world of 4K TVs with our comprehensive guide to finding the perfect display for your home entertainment setup.",
      image: "/images/4k-tv.jpg",
      category: "TVs",
      brand: "Samsung",
      publishedDate: "September 20, 2023",
      author: "Robert Chen",
      slug: "how-to-choose-right-4k-tv-2023",
      tags: ["4K", "HDR", "Smart Home", "Premium"]
    },
    {
      id: 8,
      title: "Wireless vs Wired Gaming Mice: Which Should You Choose?",
      excerpt: "Are wireless gaming mice now comparable to wired ones in performance? We dive deep into comparing the pros and cons of both.",
      image: "/images/gaming-mice.jpg",
      category: "Peripherals",
      brand: "Razer",
      publishedDate: "October 8, 2023",
      author: "Alex Wang",
      slug: "wireless-vs-wired-gaming-mice-comparison",
      tags: ["Gaming", "Wireless", "Peripherals", "Battery Life"]
    },
    {
      id: 9,
      title: "iPhone 15 Pro Max Review: The Ultimate Smartphone?",
      excerpt: "A comprehensive review of Apple's flagship iPhone, examining its new features, camera capabilities, and overall performance.",
      image: "/images/iphone-15.jpg",
      category: "Smartphones",
      brand: "Apple",
      publishedDate: "October 12, 2023",
      author: "Chris Taylor",
      slug: "iphone-15-pro-max-review",
      tags: ["Smartphones", "Premium", "Photography", "Fast Charging"]
    },
    {
      id: 10,
      title: "Samsung Galaxy Book 3 Ultra: A New Contender for Best Laptop",
      excerpt: "Samsung's premium laptop offers powerful specs and a stunning display, but is it worth the premium price tag?",
      image: "/images/galaxy-book.jpg",
      category: "Laptops",
      brand: "Samsung",
      publishedDate: "October 3, 2023",
      author: "Lisa Park",
      slug: "samsung-galaxy-book-3-ultra-review",
      tags: ["Laptops", "Premium", "Touch Screen", "High-Resolution"]
    }
  ];
  let allCategories = [];
  try {
    const categoryData = await Api.Category.getCategoryList({ page_size: 50 });
    allCategories = categoryData.category_list.map((cat) => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug
    }));
  } catch (error) {
    console.error("\u83B7\u53D6\u5206\u7C7B\u6570\u636E\u5931\u8D25:", error);
    allCategories = [];
  }
  const brands = [...new Set(allArticles.map((article) => article.brand))].filter((brand) => brand !== "Various").sort();
  const url = Astro2.url;
  const selectedCategory = url.searchParams.get("category") || "";
  const selectedBrand = url.searchParams.get("brand") || "";
  const selectedTag = url.searchParams.get("tag") || "";
  const currentPage = parseInt(url.searchParams.get("page") || "1");
  const articlesPerPage = 5;
  let filteredArticles = allArticles;
  if (selectedBrand) {
    filteredArticles = filteredArticles.filter((article) => article.brand.toLowerCase() === selectedBrand.toLowerCase());
  }
  if (selectedCategory) {
    filteredArticles = filteredArticles.filter((article) => article.category.toLowerCase() === selectedCategory.toLowerCase());
  }
  if (selectedTag) {
    filteredArticles = filteredArticles.filter(
      (article) => article.tags.some((tag) => tag.toLowerCase() === selectedTag.toLowerCase() || tag.toLowerCase().replace(/\s+/g, "-") === selectedTag.toLowerCase())
    );
  }
  const totalArticles = filteredArticles.length;
  const totalPages = Math.ceil(totalArticles / articlesPerPage);
  const paginatedArticles = filteredArticles.slice(
    (currentPage - 1) * articlesPerPage,
    currentPage * articlesPerPage
  );
  function getFilterUrl(params = {}) {
    const newParams = new URLSearchParams();
    if (selectedBrand && !("brand" in params))
      newParams.set("brand", selectedBrand);
    if (selectedCategory && !("category" in params))
      newParams.set("category", selectedCategory);
    if (selectedTag && !("tag" in params))
      newParams.set("tag", selectedTag);
    Object.entries(params).forEach(([key, value]) => {
      if (value)
        newParams.set(key, value);
    });
    return `${Astro2.url.pathname}${newParams.toString() ? `?${newParams.toString()}` : ""}`;
  }
  function getPaginationUrl(page) {
    const params = new URLSearchParams();
    if (selectedBrand)
      params.set("brand", selectedBrand);
    if (selectedCategory)
      params.set("category", selectedCategory);
    if (selectedTag)
      params.set("tag", selectedTag);
    if (page > 1)
      params.set("page", page.toString());
    return `${Astro2.url.pathname}${params.toString() ? `?${params.toString()}` : ""}`;
  }
  const popularTags = [
    { id: 1, name: "Wireless", count: 42, slug: "wireless" },
    { id: 2, name: "Budget", count: 38, slug: "budget" },
    { id: 3, name: "Premium", count: 35, slug: "premium" },
    { id: 4, name: "Gaming", count: 29, slug: "gaming" },
    { id: 5, name: "Portable", count: 24, slug: "portable" }
  ];
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": selectedBrand ? `${selectedBrand} Articles | SmartReviews` : "All Articles | SmartReviews", "description": "Browse our comprehensive collection of product reviews, buying guides, and tech comparisons to make informed purchasing decisions." }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<section class="py-12 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h1 class="text-4xl md:text-5xl font-bold mb-4"> ${selectedBrand ? `${selectedBrand} Articles` : "All Articles"} </h1> <p class="text-xl max-w-3xl mx-auto">Explore our in-depth reviews, guides, and comparisons to help you make informed purchasing decisions.</p> </div> </section>  <section class="py-6 bg-gray-50 border-b border-gray-200 sticky top-0 z-20"> <div class="container mx-auto px-4 max-w-5xl"> <!-- Active filters display --> ${(selectedBrand || selectedCategory || selectedTag) && renderTemplate`<div class="mb-4 flex items-center gap-2"> <span class="text-gray-700">Active filters:</span> ${selectedBrand && renderTemplate`<div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm"> <span>Brand: ${selectedBrand}</span> <a${addAttribute(getFilterUrl({ brand: "" }), "href")} class="ml-2 text-purple-600 hover:text-purple-800"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </a> </div>`} ${selectedCategory && renderTemplate`<div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm"> <span>Category: ${selectedCategory}</span> <a${addAttribute(getFilterUrl({ category: "" }), "href")} class="ml-2 text-purple-600 hover:text-purple-800"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </a> </div>`} ${selectedTag && renderTemplate`<div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm"> <span>Tag: ${selectedTag}</span> <a${addAttribute(getFilterUrl({ tag: "" }), "href")} class="ml-2 text-purple-600 hover:text-purple-800"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path> </svg> </a> </div>`} <a href="/articles" class="text-purple-600 hover:text-purple-800 text-sm">
Clear all
</a> </div>`} <!-- Category filters --> <div class="flex flex-wrap items-center gap-3"> <span class="font-medium text-gray-700">Categories:</span> <a${addAttribute(getFilterUrl({ category: "" }), "href")}${addAttribute(`px-3 py-1 rounded-full text-sm transition-colors ${!selectedCategory ? "bg-purple-600 text-white" : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"}`, "class")}>
All
</a> ${allCategories.map((category) => renderTemplate`<a${addAttribute(getFilterUrl({ category: category.name }), "href")}${addAttribute(`px-3 py-1 rounded-full text-sm transition-colors ${selectedCategory === category.name ? "bg-purple-600 text-white" : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"}`, "class")}> ${category.name} </a>`)} </div> </div> </section>  <section class="py-12 bg-white"> <div class="container mx-auto px-4 max-w-5xl"> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <!-- Main Content --> <div class="md:col-span-2"> <div class="mb-8 flex justify-between items-center"> <h2 class="text-2xl font-bold"> ${selectedBrand ? `${selectedBrand} Articles` : "All Articles"} ${selectedCategory ? ` - ${selectedCategory}` : ""} ${selectedTag ? ` - ${selectedTag}` : ""} </h2> <p class="text-gray-500">
Showing ${paginatedArticles.length} of ${totalArticles} articles
</p> </div> ${totalArticles === 0 ? renderTemplate`<div class="card p-8 text-center"> <p class="text-lg text-gray-600 mb-4">No articles found matching your filters.</p> <a href="/articles" class="text-purple-600 hover:text-purple-800 font-medium">View all articles</a> </div>` : renderTemplate`<div class="space-y-8"> ${paginatedArticles.map((article) => renderTemplate`<article class="card flex flex-col md:flex-row overflow-hidden hover:shadow-md transition-shadow"> <div class="md:w-1/3 relative"> <img${addAttribute(article.image, "src")}${addAttribute(article.title, "alt")} class="w-full h-full object-cover"> <span class="absolute top-4 left-4 bg-purple-600 text-white text-sm font-medium px-3 py-1 rounded-full"> ${article.category} </span> </div> <div class="md:w-2/3 p-6"> <h3 class="text-xl font-bold mb-3"> <a${addAttribute(`/article/${article.slug}`, "href")} class="text-gray-900 hover:text-purple-600"> ${article.title} </a> </h3> <p class="text-gray-600 mb-4">${article.excerpt}</p> <div class="flex flex-wrap justify-between items-center text-sm text-gray-500 gap-y-2"> <div class="flex items-center gap-2"> <span>${article.publishedDate}</span> <span>By ${article.author}</span> </div> <a${addAttribute(getFilterUrl({ brand: article.brand }), "href")} class="inline-flex items-center bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"> <span class="text-gray-700 font-medium">${article.brand}</span> </a> </div> <!-- Tags --> <div class="mt-4 flex flex-wrap gap-2"> ${article.tags.map((tag) => renderTemplate`<a${addAttribute(getFilterUrl({ tag: tag.toLowerCase().replace(/\s+/g, "-") }), "href")}${addAttribute(`text-xs px-2 py-1 rounded-full transition-colors ${selectedTag === tag.toLowerCase().replace(/\s+/g, "-") ? "bg-purple-100 text-purple-800 font-medium" : "bg-gray-100 hover:bg-gray-200 text-gray-700"}`, "class")}>
#${tag} </a>`)} </div> </div> </article>`)} </div>`} <!-- Pagination --> ${totalPages > 1 && renderTemplate`<div class="mt-12 flex justify-center"> <nav class="inline-flex rounded-md shadow"> ${currentPage > 1 && renderTemplate`<a${addAttribute(getPaginationUrl(currentPage - 1), "href")} class="py-2 px-4 border border-gray-300 bg-white rounded-l-md text-gray-700 hover:bg-gray-50">
Previous
</a>`} ${Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => renderTemplate`<a${addAttribute(getPaginationUrl(page), "href")}${addAttribute(`py-2 px-4 border-t border-b border-l border-gray-300 ${page === currentPage ? "bg-purple-50 text-purple-700 font-medium" : "bg-white text-gray-700 hover:bg-gray-50"}`, "class")}> ${page} </a>`)} ${currentPage < totalPages && renderTemplate`<a${addAttribute(getPaginationUrl(currentPage + 1), "href")} class="py-2 px-4 border border-gray-300 bg-white rounded-r-md text-gray-700 hover:bg-gray-50">
Next
</a>`} </nav> </div>`} </div> <!-- Sidebar --> <div class="md:col-span-1"> <!-- Search --> <div class="card p-6 mb-8"> <h3 class="text-xl font-bold mb-4">Search Articles</h3> <div class="relative"> <input type="text" placeholder="Search for articles..." class="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"> <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </button> </div> </div> <!-- Brand Filter --> <div class="card p-6 mb-8"> <h3 class="text-xl font-bold mb-4">Filter by Brand</h3> <!-- Brand search --> <div class="relative mb-4"> <input type="text" id="brandSearch" placeholder="Search brands..." class="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"> <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </button> </div> <!-- Popular Brands (limit to 5-10) --> <div class="mb-2"> <h4 class="font-medium text-gray-700 mb-2 text-sm">Popular Brands</h4> <div class="space-y-1"> <a${addAttribute(getFilterUrl({ brand: "" }), "href")}${addAttribute(`block w-full text-left px-3 py-2 rounded-md text-sm ${!selectedBrand ? "bg-purple-100 text-purple-800 font-medium" : "text-gray-700 hover:bg-gray-100"}`, "class")}>
All Brands
</a> ${brands.slice(0, 8).map((brand) => renderTemplate`<a${addAttribute(getFilterUrl({ brand }), "href")}${addAttribute(`block w-full text-left px-3 py-2 rounded-md text-sm ${selectedBrand === brand ? "bg-purple-100 text-purple-800 font-medium" : "text-gray-700 hover:bg-gray-100"}`, "class")}> ${brand} </a>`)} </div> </div> <!-- View all brands link --> <a href="/brands" class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center">
Browse all brands
<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> <!-- Popular Tags --> <div class="card p-6 mb-8"> <h3 class="text-xl font-bold mb-4">Popular Tags</h3> <div class="flex flex-wrap gap-2"> ${popularTags.map((tag) => renderTemplate`<a${addAttribute(`/articles?tag=${tag.slug}`, "href")}${addAttribute(`text-sm px-3 py-1 rounded-full transition-colors ${selectedTag === tag.slug ? "bg-purple-600 text-white" : "bg-gray-100 hover:bg-gray-200 text-gray-700"}`, "class")}> ${tag.name} </a>`)} <a href="/tags" class="text-purple-600 hover:text-purple-800 text-sm flex items-center ml-1">
View all tags
<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> </div> <!-- Newsletter --> <div class="card p-6 bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-100"> <h3 class="text-xl font-bold mb-4">Stay Updated</h3> <p class="text-gray-600 mb-4">Subscribe to our newsletter to receive the latest product reviews and buying guides.</p> <form class="space-y-4"> <div> <input type="email" placeholder="Your email address" class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" required> </div> <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
Subscribe
</button> </form> </div> </div> </div> </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h2 class="text-3xl md:text-4xl font-bold mb-6">Looking for Something Specific?</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">Use our advanced search to find exactly what you're looking for.</p> <div class="max-w-xl mx-auto relative"> <input type="text" placeholder="Search for products, brands, or categories..." class="w-full pl-5 pr-16 py-4 rounded-full text-gray-900 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"> <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </button> </div> </div> </section> ` })} `;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/articles.astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/articles.astro";
const $$url = "/articles";

const articles = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Articles,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { Api as A, articles as a };
