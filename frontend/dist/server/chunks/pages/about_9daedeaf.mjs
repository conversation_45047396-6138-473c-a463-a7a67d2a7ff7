/* empty css                           */import { e as createComponent, r as renderTemplate, j as renderComponent, m as maybeRenderHead, f as addAttribute, F as Fragment, u as unescapeHTML } from '../astro_0d22710d.mjs';
import { $ as $$MainLayout, s as siteConfig } from './_slug__fe545221.mjs';

const $$About = createComponent(($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & Editor-in-Chief",
      bio: "<PERSON> has 15+ years of experience in technology journalism, previously working as a senior editor at TechCrunch and The Verge before founding SmartReviews in 2019.",
      image: "/images/team/sarah-johnson.jpg"
    },
    {
      name: "<PERSON>",
      role: "Technical Director",
      bio: "With a background in electrical engineering and computer science, <PERSON> leads our testing lab and ensures that our review methodologies meet the highest standards.",
      image: "/images/team/michael-chen.jpg"
    },
    {
      name: "<PERSON>",
      role: "Senior Editor, Audio & Home Entertainment",
      bio: "<PERSON> is an audio enthusiast with 10+ years experience reviewing headphones, speakers, and home theater systems for various publications.",
      image: "/images/team/robert-williams.jpg"
    },
    {
      name: "<PERSON> <PERSON>",
      role: "Senior Editor, Mobile & Computing",
      bio: "Emily specializes in smartphones, laptops, and tablets reviews. She previously worked as a product manager at Apple and holds a degree in Computer Science.",
      image: "/images/team/emily-torres.jpg"
    },
    {
      name: "David Kim",
      role: "Senior Editor, Smart Home & Wearables",
      bio: "David covers smart home devices and wearable technology. He has a background in IoT development and consumer electronics testing.",
      image: "/images/team/david-kim.jpg"
    },
    {
      name: "Lisa Patel",
      role: "Director of Partnerships",
      bio: "Lisa manages our relationships with manufacturers, retailers, and affiliates. She has 8+ years of experience in business development in the tech industry.",
      image: "/images/team/lisa-patel.jpg"
    }
  ];
  const trustReasons = [
    {
      title: "No Paid Reviews",
      description: "Unlike many review sites, we never accept payment for reviews. Our opinions are always our own, ensuring honest and unbiased assessments of every product.",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>`
    },
    {
      title: "Rigorous Testing Process",
      description: "Every product we review undergoes thorough testing in real-world conditions. We don't just rely on specifications\u2014we verify performance claims through hands-on evaluation.",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" /></svg>`
    },
    {
      title: "Expert Reviewers",
      description: "Our review team consists of industry experts with specialized knowledge in their respective fields. Each reviewer brings years of experience and deep product category knowledge.",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>`
    },
    {
      title: "Transparent Methodology",
      description: "We publish our testing methods and criteria for each product category, allowing readers to understand exactly how we arrive at our conclusions and recommendations.",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" /></svg>`
    },
    {
      title: "Long-Term Testing",
      description: "For many products, we continue testing long after the initial review is published, providing updates on durability, reliability, and performance over time.",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>`
    },
    {
      title: "Affiliate Transparency",
      description: "While we earn commissions through affiliate links, these partnerships never influence our evaluations. Products earn our recommendation based solely on merit.",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>`
    }
  ];
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "About Us | SmartReviews", "description": "Learn about SmartReviews, our mission, our team, and our thorough review methodology for delivering honest and reliable product reviews." }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<header class="bg-gradient-to-r from-purple-700 to-purple-800 text-white py-16"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="max-w-3xl mx-auto text-center"> <h1 class="text-4xl md:text-5xl font-bold mb-6">About SmartReviews</h1> <p class="text-xl mb-8">
Our mission is to help consumers make informed purchasing decisions through honest, thorough, and unbiased product reviews.
</p> <div class="flex flex-wrap justify-center gap-4"> <a href="/contact" class="bg-white text-purple-700 hover:bg-gray-100 px-6 py-3 rounded-md font-medium transition-colors">
Contact Us
</a> <a href="/articles" class="bg-purple-600 text-white hover:bg-purple-700 px-6 py-3 rounded-md font-medium transition-colors">
Read Our Reviews
</a> </div> </div> </div> </header>  <section class="py-16 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"> <div> <h2 class="text-3xl font-bold mb-6">Our Story</h2> <p class="text-gray-600 mb-4">
SmartReviews was founded in 2019 by a team of technology journalists and product testers who were frustrated with the state of product reviews online. We noticed that many review sites were prioritizing affiliate revenue over honest assessments, leading to inflated scores and misleading recommendations.
</p> <p class="text-gray-600 mb-4">
We set out to create a different kind of review platform—one where thorough testing, technical expertise, and complete editorial independence would form the foundation of everything we publish. Our goal was simple: to become the most trusted source of product reviews on the internet.
</p> <p class="text-gray-600 mb-4">
Since our launch, we've reviewed hundreds of products across dozens of categories, helping millions of consumers make better purchasing decisions. We've turned down numerous offers to accept payment for favorable reviews, maintained strict editorial standards, and invested heavily in our testing facilities and methodologies.
</p> <p class="text-gray-600">
Today, SmartReviews continues to grow, but our founding principles remain unchanged. We're committed to providing honest, in-depth reviews that you can trust when making important purchasing decisions.
</p> </div> <div class="relative"> <div class="rounded-lg overflow-hidden shadow-xl"> <img src="/images/smartreviews-team.jpg" alt="SmartReviews team in the testing lab" class="w-full h-auto"> </div> <div class="absolute -bottom-6 -right-6 bg-purple-100 p-6 rounded-lg shadow-lg max-w-xs hidden lg:block"> <blockquote class="text-gray-700 italic">
"Our readers' trust is our most valuable asset. Everything we do is designed to earn and maintain that trust."
</blockquote> <p class="mt-4 text-sm text-gray-600 font-medium">— Sarah Johnson, Founder</p> </div> </div> </div> </div> </section>  <section class="py-16 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="text-center max-w-3xl mx-auto mb-12"> <h2 class="text-3xl font-bold mb-6">Our Core Values</h2> <p class="text-gray-600">
These principles guide everything we do at SmartReviews, from how we test products to how we present our findings.
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <div class="bg-white p-8 rounded-xl shadow-sm"> <div class="w-14 h-14 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path> </svg> </div> <h3 class="text-xl font-bold mb-3">Complete Editorial Independence</h3> <p class="text-gray-600">
Our reviews are never influenced by manufacturers, advertisers, or affiliate partnerships. We maintain strict separation between our editorial and business teams to ensure our reviewers can provide honest assessments without pressure or influence.
</p> </div> <div class="bg-white p-8 rounded-xl shadow-sm"> <div class="w-14 h-14 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path> </svg> </div> <h3 class="text-xl font-bold mb-3">Rigorous Methodology</h3> <p class="text-gray-600">
We develop and follow detailed testing protocols for each product category, ensuring consistent and objective evaluations. Our methods combine quantitative measurements with qualitative assessments based on real-world usage to provide a complete picture of each product's performance.
</p> </div> <div class="bg-white p-8 rounded-xl shadow-sm"> <div class="w-14 h-14 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg> </div> <h3 class="text-xl font-bold mb-3">Complete Transparency</h3> <p class="text-gray-600">
We disclose how we obtain review products, our testing methods, our affiliate relationships, and any potential conflicts of interest. You'll always know exactly how we came to our conclusions and what factors influenced our recommendations.
</p> </div> </div> </div> </section>  <section class="py-16 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="text-center max-w-3xl mx-auto mb-12"> <h2 class="text-3xl font-bold mb-6">Our Review Process</h2> <p class="text-gray-600">
Every SmartReviews evaluation follows a structured process designed to ensure thorough testing and fair assessment.
</p> </div> <div class="max-w-4xl mx-auto"> <div class="relative"> <!-- Timeline --> <div class="absolute left-0 top-0 h-full w-1 bg-purple-100 md:ml-6 hidden md:block"></div> <!-- Steps --> <div class="space-y-12"> <div class="relative"> <div class="flex items-start"> <div class="hidden md:block"> <div class="bg-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
1
</div> </div> <div class="md:ml-8 bg-gray-50 p-6 rounded-lg shadow-sm md:w-[calc(100%-4rem)]"> <h3 class="text-xl font-bold mb-3">Product Selection</h3> <p class="text-gray-600">
We carefully select products based on consumer interest, market significance, and innovative features. We obtain products through a combination of direct purchases, manufacturer loans, and retail samples to ensure a diverse and representative selection.
</p> </div> </div> </div> <div class="relative"> <div class="flex items-start"> <div class="hidden md:block"> <div class="bg-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
2
</div> </div> <div class="md:ml-8 bg-gray-50 p-6 rounded-lg shadow-sm md:w-[calc(100%-4rem)]"> <h3 class="text-xl font-bold mb-3">Testing Protocol Development</h3> <p class="text-gray-600">
Before testing begins, we create a detailed protocol specific to the product category. This protocol includes both objective measurements (benchmarks, battery life tests, etc.) and subjective evaluations (design quality, ease of use, etc.) to provide a comprehensive assessment.
</p> </div> </div> </div> <div class="relative"> <div class="flex items-start"> <div class="hidden md:block"> <div class="bg-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
3
</div> </div> <div class="md:ml-8 bg-gray-50 p-6 rounded-lg shadow-sm md:w-[calc(100%-4rem)]"> <h3 class="text-xl font-bold mb-3">Hands-On Testing</h3> <p class="text-gray-600">
Our expert reviewers use each product extensively in real-world conditions—using smartphones as daily drivers, cooking multiple meals with kitchen appliances, or living with smart home devices. This hands-on approach reveals strengths and weaknesses that lab testing alone might miss.
</p> </div> </div> </div> <div class="relative"> <div class="flex items-start"> <div class="hidden md:block"> <div class="bg-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
4
</div> </div> <div class="md:ml-8 bg-gray-50 p-6 rounded-lg shadow-sm md:w-[calc(100%-4rem)]"> <h3 class="text-xl font-bold mb-3">Comparative Analysis</h3> <p class="text-gray-600">
We evaluate products not just on their own merits, but also in comparison to similar offerings in the market. This allows us to provide context for our assessments and help readers understand how a product stacks up against alternatives at various price points.
</p> </div> </div> </div> <div class="relative"> <div class="flex items-start"> <div class="hidden md:block"> <div class="bg-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
5
</div> </div> <div class="md:ml-8 bg-gray-50 p-6 rounded-lg shadow-sm md:w-[calc(100%-4rem)]"> <h3 class="text-xl font-bold mb-3">Editorial Review</h3> <p class="text-gray-600">
Before publication, each review undergoes a thorough editorial process. Editors check for accuracy, clarity, and adherence to our methodologies and standards. This multi-layer review ensures the quality and integrity of our content.
</p> </div> </div> </div> <div class="relative"> <div class="flex items-start"> <div class="hidden md:block"> <div class="bg-purple-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold">
6
</div> </div> <div class="md:ml-8 bg-gray-50 p-6 rounded-lg shadow-sm md:w-[calc(100%-4rem)]"> <h3 class="text-xl font-bold mb-3">Long-Term Testing</h3> <p class="text-gray-600">
For many products, our testing continues long after the initial review is published. We update reviews with insights on durability, software updates, and long-term performance, providing readers with a more complete picture of a product's lifecycle.
</p> </div> </div> </div> </div> </div> </div> </div> </section>  <section class="py-16 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="text-center max-w-3xl mx-auto mb-12"> <h2 class="text-3xl font-bold mb-6">Meet Our Team</h2> <p class="text-gray-600">
Our reviews are created by a diverse team of experts with deep knowledge in their respective product categories.
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${teamMembers.map((member) => renderTemplate`<div class="bg-white rounded-xl overflow-hidden shadow-sm"> <div class="h-64 overflow-hidden"> <img${addAttribute(member.image, "src")}${addAttribute(member.name, "alt")} class="w-full h-full object-cover transition-transform hover:scale-105"> </div> <div class="p-6"> <h3 class="text-xl font-bold text-gray-900">${member.name}</h3> <p class="text-purple-600 font-medium mb-3">${member.role}</p> <p class="text-gray-600">${member.bio}</p> </div> </div>`)} </div> </div> </section>  <section class="py-16 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="text-center max-w-3xl mx-auto mb-12"> <h2 class="text-3xl font-bold mb-6">Why Trust SmartReviews?</h2> <p class="text-gray-600">
Our commitment to honest, thorough, and unbiased reviews sets us apart in an industry where integrity is often compromised.
</p> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${trustReasons.map((reason) => renderTemplate`<div class="p-6 bg-gray-50 rounded-xl"> <div class="text-purple-600 mb-4"> ${renderComponent($$result2, "Fragment", Fragment, {}, { "default": ($$result3) => renderTemplate`${unescapeHTML(reason.icon)}` })} </div> <h3 class="text-xl font-bold mb-3">${reason.title}</h3> <p class="text-gray-600">${reason.description}</p> </div>`)} </div> </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center`, "class")}> <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Make Smarter Purchase Decisions?</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">
Explore our latest reviews and buying guides to find the perfect products for your needs and budget.
</p> <div class="flex flex-wrap justify-center gap-4"> <a href="/articles" class="bg-white text-purple-700 hover:bg-gray-100 px-6 py-3 rounded-md font-medium transition-colors">
Browse Reviews
</a> <a href="/category" class="bg-purple-600 text-white hover:bg-purple-700 border border-purple-500 px-6 py-3 rounded-md font-medium transition-colors">
Explore Categories
</a> </div> </div> </section> ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/about.astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/about.astro";
const $$url = "/about";

export { $$About as default, $$file as file, $$url as url };
