/* empty css                           */import { g as create<PERSON><PERSON>, e as createComponent, r as renderTemplate, j as renderComponent, m as maybeRenderHead, f as addAttribute } from '../astro_0d22710d.mjs';
import { $ as $$MainLayout, s as siteConfig } from './_slug__fe545221.mjs';

const $$Astro = createAstro("https://smartreviews.com");
const $$Deals = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Deals;
  const { containerWidth } = siteConfig;
  const currentPage = parseInt(Astro2.url.searchParams.get("page") || "1");
  const itemsPerPage = 8;
  const deals = [
    {
      id: 1,
      deal_name: "Target Test Prep: 5-Day Full-Access Trail for Free",
      discount: "Trail for Free",
      description: "Target Test Prep has 5-Day Full-Access Trail for Free. No code needed. Offer may end soon.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/25174556096569498.png",
      begin_date: "2025-04-27",
      end_date: "2030-04-27",
      origin_url: "https://gmat.targettestprep.com/",
      tracking_url: "https://www.linkbux.com/track/c0f4e8skl_bU_aC_bGGPHdEc2BAB1_a1oY5jtRXb4GAQj1nLILvwQVbGr1Iaa1z3YdsYWD_bFFX_agWudKpsv3?url=https%3A%2F%2Fgmat.targettestprep.com%2F",
      category: "Education"
    },
    {
      id: 2,
      deal_name: "Coursera Plus: First 14 Days Free Trial",
      discount: "14 Days Free",
      description: "Get unlimited access to 7,000+ world-class courses, hands-on projects, and job-ready certificate programs for 14 days free.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/coursera-trial.png",
      begin_date: "2025-04-20",
      end_date: "2025-07-20",
      origin_url: "https://www.coursera.org/",
      tracking_url: "https://www.linkbux.com/track/coursera-plus-trial",
      category: "Education"
    },
    {
      id: 3,
      deal_name: "Udemy: Web Development Bootcamp 92% OFF",
      discount: "92% OFF",
      description: "Complete 2025 Web Development Bootcamp by Dr. Angela Yu now available with huge discount. Limited time offer.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/udemy-webdev.png",
      begin_date: "2025-04-15",
      end_date: "2025-05-15",
      origin_url: "https://www.udemy.com/course/the-complete-web-development-bootcamp/",
      tracking_url: "https://www.linkbux.com/track/udemy-webdev-bootcamp",
      category: "Education"
    },
    {
      id: 4,
      deal_name: "Skillshare Premium: First Month Free",
      discount: "First Month Free",
      description: "Explore thousands of creative classes with one month of free access to Skillshare Premium. Cancel anytime before trial ends.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/skillshare-premium.png",
      begin_date: "2025-04-01",
      end_date: "2025-06-30",
      origin_url: "https://www.skillshare.com/",
      tracking_url: "https://www.linkbux.com/track/skillshare-premium-trial",
      category: "Education"
    },
    {
      id: 5,
      deal_name: "DataCamp: 67% OFF Annual Subscription",
      discount: "67% OFF",
      description: "Learn data science and analytics skills with interactive coding challenges. Limited time discount on annual plans.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/datacamp-offer.png",
      begin_date: "2025-04-10",
      end_date: "2025-05-10",
      origin_url: "https://www.datacamp.com/",
      tracking_url: "https://www.linkbux.com/track/datacamp-annual-discount",
      category: "Education"
    },
    {
      id: 6,
      deal_name: "LinkedIn Learning: Free Month of Premium",
      discount: "1 Month Free",
      description: "Access 16,000+ expert-led LinkedIn Learning courses with a free month of Premium. Build business, creative, and tech skills.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/linkedin-learning.png",
      begin_date: "2025-04-05",
      end_date: "2025-07-05",
      origin_url: "https://www.linkedin.com/learning/",
      tracking_url: "https://www.linkbux.com/track/linkedin-learning-trial",
      category: "Education"
    },
    {
      id: 7,
      deal_name: "MasterClass Annual Membership: Buy 1 Get 1 Free",
      discount: "Buy 1 Get 1 Free",
      description: "Share the gift of learning with MasterClass 2-for-1 annual membership offer. Learn from the world's best instructors.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/masterclass-bogo.png",
      begin_date: "2025-04-22",
      end_date: "2025-05-31",
      origin_url: "https://www.masterclass.com/",
      tracking_url: "https://www.linkbux.com/track/masterclass-bogo-offer",
      category: "Education"
    },
    {
      id: 8,
      deal_name: "Brilliant Premium: 20% OFF Annual Plan",
      discount: "20% OFF",
      description: "Develop problem-solving skills with interactive courses in math, science, and computer science at a special discount.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/brilliant-premium.png",
      begin_date: "2025-04-18",
      end_date: "2025-05-18",
      origin_url: "https://brilliant.org/",
      tracking_url: "https://www.linkbux.com/track/brilliant-annual-discount",
      category: "Education"
    },
    {
      id: 9,
      deal_name: "Codecademy Pro: 40% OFF Annual Subscription",
      discount: "40% OFF",
      description: "Learn to code with interactive lessons at a significant discount. Perfect for beginners and intermediate programmers.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/codecademy-pro-discount.png",
      begin_date: "2025-04-12",
      end_date: "2025-05-26",
      origin_url: "https://www.codecademy.com/",
      tracking_url: "https://www.linkbux.com/track/codecademy-pro-discount",
      category: "Education"
    },
    {
      id: 10,
      deal_name: "Rosetta Stone: Lifetime Access 50% OFF",
      discount: "50% OFF",
      description: "Learn a new language with lifetime access to all 25 languages at half price. Award-winning language learning software.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/rosetta-stone.png",
      begin_date: "2025-04-25",
      end_date: "2025-06-25",
      origin_url: "https://www.rosettastone.com/",
      tracking_url: "https://www.linkbux.com/track/rosetta-stone-lifetime",
      category: "Education"
    }
  ];
  const calculateRemainingDays = (endDate) => {
    const end = new Date(endDate);
    const now = /* @__PURE__ */ new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1e3 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };
  const dealsWithDaysRemaining = deals.map((deal) => {
    const daysRemaining = calculateRemainingDays(deal.end_date);
    return {
      ...deal,
      daysRemaining
    };
  });
  const categories = [...new Set(deals.map((deal) => deal.category))];
  const totalItems = dealsWithDaysRemaining.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const paginatedDeals = dealsWithDaysRemaining.slice(startIndex, endIndex);
  const paginationLinks = [];
  for (let i = 1; i <= totalPages; i++) {
    paginationLinks.push(i);
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Hot Deals & Discounts | SmartReviews", "description": "Find the best deals and discounts on education courses, learning platforms, and more. Limited-time offers from top providers." }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<section class="bg-gradient-to-r from-purple-700 to-purple-800 text-white py-12"> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center`, "class")}> <h1 class="text-4xl md:text-5xl font-bold mb-4">Today's Hot Deals</h1> <p class="text-xl max-w-3xl mx-auto">Exclusive discounts on top-rated educational resources and courses. Click any deal to claim your offer.</p> </div> </section>  <section class="bg-white sticky top-16 z-10 py-4 border-b border-gray-100 shadow-sm"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="flex flex-wrap items-center gap-2"> <span class="text-gray-700 font-medium mr-2">Filter by:</span> <a href="#all" class="px-3 py-1.5 bg-purple-600 text-white rounded-full text-sm font-medium">All Deals</a> ${categories.map((category) => renderTemplate`<a${addAttribute(`#${category.toLowerCase().replace(/\s+/g, "-")}`, "href")} class="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium transition-colors"> ${category} </a>`)} </div> </div> </section>  <section class="py-12 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${paginatedDeals.map((deal) => renderTemplate`<a${addAttribute(deal.tracking_url, "href")} target="_blank" rel="noopener" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all hover:scale-[1.02] border border-gray-100 block group"> <div class="relative"> <img${addAttribute(deal.img, "src")}${addAttribute(deal.deal_name, "alt")} class="w-full h-48 object-cover"> <div class="absolute top-3 right-3 bg-purple-500 text-white text-xs font-bold px-2 py-1 rounded-full">${deal.discount}</div> <div class="absolute bottom-0 left-0 bg-gradient-to-t from-black/70 to-transparent w-full h-12"></div> <div class="absolute bottom-2 left-3 flex space-x-2"> <span class="inline-block px-2 py-0.5 bg-white/20 backdrop-blur-sm text-white text-xs rounded">${deal.category}</span> ${deal.daysRemaining > 0 && deal.daysRemaining <= 30 && renderTemplate`<span class="inline-block px-2 py-0.5 bg-orange-500/90 text-white text-xs rounded">${deal.daysRemaining} days left</span>`} </div> </div> <div class="p-4"> <div class="mb-2"> <h3 class="text-lg font-semibold text-gray-900 group-hover:text-purple-700 transition-colors line-clamp-2">${deal.deal_name}</h3> </div> <p class="text-sm text-gray-600 mb-3 line-clamp-3">${deal.description}</p> <div class="flex justify-between items-center"> <span class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded"> ${new Date(deal.begin_date).toLocaleDateString("en-US", { year: "numeric", month: "short", day: "numeric" })} </span> </div> <div class="mt-3 text-center"> <span class="inline-block w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm font-medium transition-colors">
Access Deal
</span> </div> </div> </a>`)} </div> <!-- 分页控件 --> ${totalPages > 1 && renderTemplate`<div class="flex justify-center mt-10"> <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination"> <!-- 上一页按钮 --> <a${addAttribute(currentPage > 1 ? `?page=${currentPage - 1}` : "#", "href")}${addAttribute(`relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? "text-gray-300 cursor-not-allowed" : "text-gray-700 hover:bg-gray-50"}`, "class")}> <span class="sr-only">Previous</span> <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"> <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </a> <!-- 页码 --> ${paginationLinks.map((page) => renderTemplate`<a${addAttribute(`?page=${page}`, "href")}${addAttribute(`relative inline-flex items-center px-4 py-2 border ${page === currentPage ? "bg-purple-50 border-purple-500 text-purple-600 z-10" : "bg-white border-gray-300 text-gray-700 hover:bg-gray-50"} text-sm font-medium`, "class")}> ${page} </a>`)} <!-- 下一页按钮 --> <a${addAttribute(currentPage < totalPages ? `?page=${currentPage + 1}` : "#", "href")}${addAttribute(`relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? "text-gray-300 cursor-not-allowed" : "text-gray-700 hover:bg-gray-50"}`, "class")}> <span class="sr-only">Next</span> <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"> <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path> </svg> </a> </nav> </div>`} </div> </section>  <section class="py-12 bg-purple-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center`, "class")}> <h2 class="text-2xl md:text-3xl font-bold mb-4">Never Miss a Deal</h2> <p class="text-gray-600 max-w-2xl mx-auto mb-6">Subscribe to our newsletter and be the first to know about exclusive discounts and offers on educational resources.</p> <form class="max-w-md mx-auto flex"> <input type="email" placeholder="Your email address" class="flex-grow px-4 py-3 rounded-l-md focus:outline-none focus:ring-2 focus:ring-purple-500 border border-gray-300 text-gray-600" required> <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-r-md font-medium transition-colors">
Subscribe
</button> </form> <p class="text-sm text-gray-500 mt-4">We respect your privacy. Unsubscribe at any time.</p> </div> </section> ` })} `;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/deals.astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/deals.astro";
const $$url = "/deals";

export { $$Deals as default, $$file as file, $$url as url };
