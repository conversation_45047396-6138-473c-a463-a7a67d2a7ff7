/* empty css                           */import { e as createComponent, r as renderTemplate, m as maybeRenderHead, f as addAttribute, g as createAstro, j as renderComponent, F as Fragment, u as unescapeHTML, k as defineScriptVars } from '../astro_0d22710d.mjs';
import { s as siteConfig, $ as $$MainLayout } from './_slug__fe545221.mjs';
import 'clsx';
import { A as Api } from './articles_2badbe14.mjs';

const $$Hero = createComponent(($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  return renderTemplate`${maybeRenderHead()}<section class="relative bg-white text-gray-800 pt-2 pb-4"> <div${addAttribute(`container mx-auto px-3 sm:px-4 ${containerWidth}`, "class")}> <div class="grid grid-cols-1 lg:grid-cols-12 gap-1 lg:gap-2 items-start"> <!-- Main article --> <div class="lg:col-span-6 group relative h-full"> <div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl blur opacity-20 group-hover:opacity-40 transition duration-300 group-hover:duration-200"></div> <a href="/articles/wisp-herpes-treatment" class="block relative overflow-hidden rounded-2xl bg-white transform hover:scale-[1.01] transition-all duration-300 shadow-lg hover:shadow-purple-500/10 h-full"> <div class="aspect-[16/8] relative overflow-hidden"> <img src="https://example.com/hero-image.jpg" alt="Wisp's Herpes Treatment Review" class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"> <div class="absolute inset-0 bg-gradient-to-t from-gray-800/70 via-gray-900/30 to-transparent"></div> <div class="absolute bottom-0 left-0 p-4"> <span class="inline-flex items-center px-2 py-0.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium rounded-full shadow-sm"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path> </svg>
Reviews
</span> </div> </div> <div class="p-3 md:p-4 relative bg-white"> <div class="flex items-center justify-between mb-1"> <span class="text-sm text-gray-400 font-medium">26 Mar 2025</span> </div> <h3 class="text-xl md:text-2xl font-bold mb-2 text-gray-800 group-hover:text-purple-500 transition-colors">Wisp's Herpes Treatment Review: Is This the Modern Solution You've Been Looking For?</h3> <p class="text-gray-600 mb-3 text-sm line-clamp-2">In-depth analysis of Wisp's herpes treatment plan, exploring its innovation, effectiveness, and how it changes traditional treatment approaches...</p> <div class="flex items-center justify-between"> <div class="flex items-center"> <img src="https://randomuser.me/api/portraits/women/1.jpg" alt="Author" class="w-6 h-6 rounded-full border-2 border-purple-500"> <span class="ml-2 text-sm text-gray-500">By Sarah Johnson</span> </div> <span class="text-purple-500 group-hover:translate-x-2 transition-transform duration-300 inline-flex items-center font-medium">
Read More
<svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path> </svg> </span> </div> </div> </a> </div> <!-- Secondary articles list --> <div class="lg:col-span-6 h-full flex flex-col justify-between gap-2"> <!-- Secondary article 1 --> <div class="relative group h-[calc(25%-4px)]"> <div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl blur opacity-10 group-hover:opacity-30 transition duration-1000 group-hover:duration-200"></div> <a href="/guides/sports-lover-gift-guide" class="block bg-white rounded-xl overflow-hidden transform hover:scale-[1.01] transition-all duration-300 relative shadow-md hover:shadow-purple-500/10 h-full"> <div class="flex items-start p-0 h-full"> <div class="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden"> <img src="https://example.com/sports-gift.jpg" alt="Sports Lover's Gift Guide" class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"> </div> <div class="ml-2 flex-1 py-1"> <div class="flex items-center justify-between"> <span class="inline-flex items-center px-1.5 py-0.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium rounded-full shadow-sm"> <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor"> <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path> </svg>
Guide
</span> <span class="text-xs text-gray-400">05 Mar 2025</span> </div> <h3 class="text-sm lg:text-base font-semibold text-gray-800 group-hover:text-purple-500 transition-colors line-clamp-2">The Ultimate Sports Lover's Gift Guide 2025: Top Picks for Every Fan</h3> </div> </div> </a> </div> <!-- Secondary article 2 --> <div class="relative group h-[calc(25%-4px)]"> <div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl blur opacity-10 group-hover:opacity-30 transition duration-1000 group-hover:duration-200"></div> <a href="/accessories/brilliant-earth-rings" class="block bg-white rounded-xl overflow-hidden transform hover:scale-[1.01] transition-all duration-300 relative shadow-md hover:shadow-purple-500/10 h-full"> <div class="flex items-start p-0 h-full"> <div class="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden"> <img src="https://example.com/rings.jpg" alt="Brilliant Earth Engagement Rings" class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"> </div> <div class="ml-2 flex-1 py-1"> <div class="flex items-center justify-between"> <span class="inline-flex items-center px-1.5 py-0.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium rounded-full shadow-sm"> <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clip-rule="evenodd"></path> </svg>
Accessories
</span> <span class="text-xs text-gray-400">29 Oct 2024</span> </div> <h3 class="text-sm lg:text-base font-semibold text-gray-800 group-hover:text-purple-500 transition-colors line-clamp-2">Brilliant Earth Engagement Rings Review</h3> </div> </div> </a> </div> <!-- Secondary article 3 --> <div class="relative group h-[calc(25%-4px)]"> <div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl blur opacity-10 group-hover:opacity-30 transition duration-1000 group-hover:duration-200"></div> <a href="/best/telehealth-services" class="block bg-white rounded-xl overflow-hidden transform hover:scale-[1.01] transition-all duration-300 relative shadow-md hover:shadow-purple-500/10 h-full"> <div class="flex items-start p-0 h-full"> <div class="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden"> <img src="https://example.com/telehealth.jpg" alt="Best Telehealth Services" class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"> </div> <div class="ml-2 flex-1 py-1"> <div class="flex items-center justify-between"> <span class="inline-flex items-center px-1.5 py-0.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium rounded-full shadow-sm"> <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg>
Best
</span> <span class="text-xs text-gray-400">26 Mar 2025</span> </div> <h3 class="text-sm lg:text-base font-semibold text-gray-800 group-hover:text-purple-500 transition-colors line-clamp-2">10 Best Telehealth Services</h3> </div> </div> </a> </div> <!-- Secondary article 4 (New) --> <div class="relative group h-[calc(25%-4px)] hidden lg:block"> <div class="absolute -inset-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl blur opacity-10 group-hover:opacity-30 transition duration-1000 group-hover:duration-200"></div> <a href="/tech/best-wireless-earbuds" class="block bg-white rounded-xl overflow-hidden transform hover:scale-[1.01] transition-all duration-300 relative shadow-md hover:shadow-purple-500/10 h-full"> <div class="flex items-start p-0 h-full"> <div class="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden"> <img src="https://example.com/earbuds.jpg" alt="Best Wireless Earbuds" class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-700"> </div> <div class="ml-2 flex-1 py-1"> <div class="flex items-center justify-between"> <span class="inline-flex items-center px-1.5 py-0.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium rounded-full shadow-sm"> <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.343c2.673 0 4.012-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z" clip-rule="evenodd"></path> </svg>
Tech
</span> <span class="text-xs text-gray-400">15 Mar 2025</span> </div> <h3 class="text-sm lg:text-base font-semibold text-gray-800 group-hover:text-purple-500 transition-colors line-clamp-2">The Ultimate Guide to Wireless Earbuds: Top Picks for 2025</h3> </div> </div> </a> </div> </div> </div> </div> <!-- Decorative elements --> <div class="absolute top-0 right-0 -z-10 w-32 h-32 bg-purple-100 rounded-full opacity-20 blur-3xl"></div> <div class="absolute bottom-0 left-1/4 -z-10 w-40 h-40 bg-purple-200 rounded-full opacity-20 blur-3xl"></div> </section>`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/components/Hero.astro", void 0);

const $$FeaturedCarousel = createComponent(($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  return renderTemplate`${maybeRenderHead()}<section class="py-16 sm:py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden"> <!-- Decorative elements --> <div class="absolute top-0 left-0 w-64 h-64 bg-purple-100 rounded-full opacity-30 blur-3xl -translate-x-1/2 -translate-y-1/2"></div> <div class="absolute bottom-0 right-0 w-96 h-96 bg-purple-50 rounded-full opacity-40 blur-3xl translate-x-1/4 translate-y-1/4"></div> <div${addAttribute(`container mx-auto px-3 sm:px-4 ${containerWidth} relative`, "class")}> <div class="text-center mb-16 max-w-3xl mx-auto"> <span class="inline-block px-4 py-1.5 text-sm font-medium text-purple-800 bg-purple-100 rounded-full mb-4 shadow-sm transform hover:scale-105 transition-transform duration-300">Featured Content</span> <h2 class="text-3xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight bg-clip-text text-transparent bg-gradient-to-r from-purple-900 to-purple-600">Editor's Picks</h2> <p class="text-xl text-gray-600 mx-auto leading-relaxed">Our most insightful and helpful articles selected by our editors.</p> </div> <!-- Carousel implementation with navigation arrows --> <div class="relative"> <!-- Left Arrow --> <button id="carousel-prev" class="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 lg:-translate-x-6 z-10 bg-white p-2.5 rounded-full shadow-lg hover:shadow-purple-200 hover:bg-purple-50 focus:outline-none transition-all duration-300 group"> <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-800 group-hover:text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path> </svg> </button> <!-- Carousel Container --> <div id="carousel-container" class="overflow-hidden rounded-2xl"> <div id="carousel-track" class="flex transition-transform duration-700 ease-out"> <!-- Slide 1 --> <div class="carousel-slide min-w-full"> <div class="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8"> <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 flex flex-col hover:shadow-2xl hover:shadow-purple-100 transform hover:scale-[1.01] transition-all duration-500 ease-out"> <div class="relative pb-[60%] overflow-hidden"> <img src="/images/headphones.jpg" alt="Top Wireless Headphones" class="absolute inset-0 w-full h-full object-cover transform hover:scale-110 transition-transform duration-700"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div> <div class="absolute top-4 left-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium px-3 py-1 rounded-full shadow-lg">
Featured
</div> </div> <div class="p-6 md:p-8 flex-grow flex flex-col"> <h3 class="text-xl md:text-2xl font-bold mb-3 line-clamp-2"> <a href="/article/top-10-wireless-headphones-2023" class="text-gray-900 hover:text-purple-600 transition-colors">
Top 10 Wireless Headphones for 2023
</a> </h3> <p class="text-gray-600 mb-6 flex-grow">Discover the best wireless headphones that combine superior sound quality, comfort, and battery life for an exceptional listening experience.</p> <div class="flex justify-between items-center text-sm"> <span class="text-gray-500">October 15, 2023</span> <span class="flex items-center"> <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="John Smith" class="w-6 h-6 rounded-full mr-2 border border-purple-200"> <span class="text-gray-700 font-medium">John Smith</span> </span> </div> </div> </div> <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 flex flex-col hover:shadow-2xl hover:shadow-purple-100 transform hover:scale-[1.01] transition-all duration-500 ease-out"> <div class="relative pb-[60%] overflow-hidden"> <img src="/images/gaming-laptop.jpg" alt="Gaming Laptop Guide" class="absolute inset-0 w-full h-full object-cover transform hover:scale-110 transition-transform duration-700"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div> <div class="absolute top-4 left-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium px-3 py-1 rounded-full shadow-lg">
Featured
</div> </div> <div class="p-6 md:p-8 flex-grow flex flex-col"> <h3 class="text-xl md:text-2xl font-bold mb-3 line-clamp-2"> <a href="/article/ultimate-guide-gaming-laptop" class="text-gray-900 hover:text-purple-600 transition-colors">
The Ultimate Guide to Choosing a Gaming Laptop
</a> </h3> <p class="text-gray-600 mb-6 flex-grow">Everything you need to know about selecting the perfect gaming laptop that meets your performance needs and budget constraints.</p> <div class="flex justify-between items-center text-sm"> <span class="text-gray-500">October 10, 2023</span> <span class="flex items-center"> <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Emily Johnson" class="w-6 h-6 rounded-full mr-2 border border-purple-200"> <span class="text-gray-700 font-medium">Emily Johnson</span> </span> </div> </div> </div> </div> </div> <!-- Slide 2 --> <div class="carousel-slide min-w-full"> <div class="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8"> <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 flex flex-col hover:shadow-2xl hover:shadow-purple-100 transform hover:scale-[1.01] transition-all duration-500 ease-out"> <div class="relative pb-[60%] overflow-hidden"> <img src="/images/smart-home.jpg" alt="Smart Home Devices" class="absolute inset-0 w-full h-full object-cover transform hover:scale-110 transition-transform duration-700"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div> <div class="absolute top-4 left-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium px-3 py-1 rounded-full shadow-lg">
Featured
</div> </div> <div class="p-6 md:p-8 flex-grow flex flex-col"> <h3 class="text-xl md:text-2xl font-bold mb-3 line-clamp-2"> <a href="/article/smart-home-devices-save-money" class="text-gray-900 hover:text-purple-600 transition-colors">
Smart Home Devices That Actually Save You Money
</a> </h3> <p class="text-gray-600 mb-6 flex-grow">These smart home gadgets not only make your life more convenient but also help reduce your monthly bills significantly.</p> <div class="flex justify-between items-center text-sm"> <span class="text-gray-500">October 5, 2023</span> <span class="flex items-center"> <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="Michael Brown" class="w-6 h-6 rounded-full mr-2 border border-purple-200"> <span class="text-gray-700 font-medium">Michael Brown</span> </span> </div> </div> </div> <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 flex flex-col hover:shadow-2xl hover:shadow-purple-100 transform hover:scale-[1.01] transition-all duration-500 ease-out"> <div class="relative pb-[60%] overflow-hidden"> <img src="/images/budget-smartphone.jpg" alt="Budget Smartphones" class="absolute inset-0 w-full h-full object-cover transform hover:scale-110 transition-transform duration-700"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div> <div class="absolute top-4 left-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium px-3 py-1 rounded-full shadow-lg">
Featured
</div> </div> <div class="p-6 md:p-8 flex-grow flex flex-col"> <h3 class="text-xl md:text-2xl font-bold mb-3 line-clamp-2"> <a href="/article/best-budget-smartphones-under-300" class="text-gray-900 hover:text-purple-600 transition-colors">
Best Budget Smartphones Under $300
</a> </h3> <p class="text-gray-600 mb-6 flex-grow">Quality smartphones don't have to break the bank. Here are our top picks for budget-friendly options in 2023.</p> <div class="flex justify-between items-center text-sm"> <span class="text-gray-500">October 1, 2023</span> <span class="flex items-center"> <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Sarah Wilson" class="w-6 h-6 rounded-full mr-2 border border-purple-200"> <span class="text-gray-700 font-medium">Sarah Wilson</span> </span> </div> </div> </div> </div> </div> </div> </div> <!-- Right Arrow --> <button id="carousel-next" class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 lg:translate-x-6 z-10 bg-white p-2.5 rounded-full shadow-lg hover:shadow-purple-200 hover:bg-purple-50 focus:outline-none transition-all duration-300 group"> <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-800 group-hover:text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </button> <!-- Carousel Indicators --> <div class="flex justify-center mt-10"> <div class="flex items-center space-x-3 bg-white rounded-full px-4 py-2 shadow-md"> <button class="carousel-indicator w-3 h-3 rounded-full bg-purple-600 transition-all duration-300 hover:scale-125" data-slide="0"></button> <button class="carousel-indicator w-3 h-3 rounded-full bg-gray-300 transition-all duration-300 hover:scale-125" data-slide="1"></button> </div> </div> </div> </div> </section> `;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/components/FeaturedCarousel.astro", void 0);

const $$DealsSection = createComponent(($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  const deals = [
    {
      id: 1,
      deal_name: "Target Test Prep: 5-Day Full-Access Trail for Free",
      discount: "Trail for Free",
      description: "Target Test Prep has 5-Day Full-Access Trail for Free. No code needed. Offer may end soon.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/25174556096569498.png",
      begin_date: "2025-04-27",
      end_date: "2030-04-27",
      origin_url: "https://gmat.targettestprep.com/",
      tracking_url: "https://www.linkbux.com/track/c0f4e8skl_bU_aC_bGGPHdEc2BAB1_a1oY5jtRXb4GAQj1nLILvwQVbGr1Iaa1z3YdsYWD_bFFX_agWudKpsv3?url=https%3A%2F%2Fgmat.targettestprep.com%2F",
      category: "Education"
    },
    {
      id: 2,
      deal_name: "Coursera Plus: First 14 Days Free Trial",
      discount: "14 Days Free",
      description: "Get unlimited access to 7,000+ world-class courses, hands-on projects, and job-ready certificate programs for 14 days free.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/coursera-trial.png",
      begin_date: "2025-04-20",
      end_date: "2025-07-20",
      origin_url: "https://www.coursera.org/",
      tracking_url: "https://www.linkbux.com/track/coursera-plus-trial",
      category: "Education"
    },
    {
      id: 3,
      deal_name: "Udemy: Web Development Bootcamp 92% OFF",
      discount: "92% OFF",
      description: "Complete 2025 Web Development Bootcamp by Dr. Angela Yu now available with huge discount. Limited time offer.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/udemy-webdev.png",
      begin_date: "2025-04-15",
      end_date: "2025-05-15",
      origin_url: "https://www.udemy.com/course/the-complete-web-development-bootcamp/",
      tracking_url: "https://www.linkbux.com/track/udemy-webdev-bootcamp",
      category: "Education"
    },
    {
      id: 4,
      deal_name: "Skillshare Premium: First Month Free",
      discount: "First Month Free",
      description: "Explore thousands of creative classes with one month of free access to Skillshare Premium. Cancel anytime before trial ends.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/skillshare-premium.png",
      begin_date: "2025-04-01",
      end_date: "2025-06-30",
      origin_url: "https://www.skillshare.com/",
      tracking_url: "https://www.linkbux.com/track/skillshare-premium-trial",
      category: "Education"
    },
    {
      id: 5,
      deal_name: "DataCamp: 67% OFF Annual Subscription",
      discount: "67% OFF",
      description: "Learn data science and analytics skills with interactive coding challenges. Limited time discount on annual plans.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/datacamp-offer.png",
      begin_date: "2025-04-10",
      end_date: "2025-05-10",
      origin_url: "https://www.datacamp.com/",
      tracking_url: "https://www.linkbux.com/track/datacamp-annual-discount",
      category: "Education"
    },
    {
      id: 6,
      deal_name: "LinkedIn Learning: Free Month of Premium",
      discount: "1 Month Free",
      description: "Access 16,000+ expert-led LinkedIn Learning courses with a free month of Premium. Build business, creative, and tech skills.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/linkedin-learning.png",
      begin_date: "2025-04-05",
      end_date: "2025-07-05",
      origin_url: "https://www.linkedin.com/learning/",
      tracking_url: "https://www.linkbux.com/track/linkedin-learning-trial",
      category: "Education"
    },
    {
      id: 7,
      deal_name: "MasterClass Annual Membership: Buy 1 Get 1 Free",
      discount: "Buy 1 Get 1 Free",
      description: "Share the gift of learning with MasterClass 2-for-1 annual membership offer. Learn from the world's best instructors.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/masterclass-bogo.png",
      begin_date: "2025-04-22",
      end_date: "2025-05-31",
      origin_url: "https://www.masterclass.com/",
      tracking_url: "https://www.linkbux.com/track/masterclass-bogo-offer",
      category: "Education"
    },
    {
      id: 8,
      deal_name: "Brilliant Premium: 20% OFF Annual Plan",
      discount: "20% OFF",
      description: "Develop problem-solving skills with interactive courses in math, science, and computer science at a special discount.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/brilliant-premium.png",
      begin_date: "2025-04-18",
      end_date: "2025-05-18",
      origin_url: "https://brilliant.org/",
      tracking_url: "https://www.linkbux.com/track/brilliant-annual-discount",
      category: "Education"
    },
    {
      id: 9,
      deal_name: "Codecademy Pro: 40% OFF Annual Subscription",
      discount: "40% OFF",
      description: "Learn to code with interactive lessons at a significant discount. Perfect for beginners and intermediate programmers.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/codecademy-pro-discount.png",
      begin_date: "2025-04-12",
      end_date: "2025-05-26",
      origin_url: "https://www.codecademy.com/",
      tracking_url: "https://www.linkbux.com/track/codecademy-pro-discount",
      category: "Education"
    },
    {
      id: 10,
      deal_name: "Rosetta Stone: Lifetime Access 50% OFF",
      discount: "50% OFF",
      description: "Learn a new language with lifetime access to all 25 languages at half price. Award-winning language learning software.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/rosetta-stone.png",
      begin_date: "2025-04-25",
      end_date: "2025-06-25",
      origin_url: "https://www.rosettastone.com/",
      tracking_url: "https://www.linkbux.com/track/rosetta-stone-lifetime",
      category: "Education"
    },
    {
      id: 11,
      deal_name: "Khan Academy: Support Education For All",
      discount: "Free Access",
      description: "Khan Academy provides world-class education for free. Consider supporting their mission with a donation.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/khan-academy.png",
      begin_date: "2025-04-15",
      end_date: "2025-12-31",
      origin_url: "https://www.khanacademy.org/",
      tracking_url: "https://www.linkbux.com/track/khan-academy-support",
      category: "Education"
    },
    {
      id: 12,
      deal_name: "Duolingo Plus: 30% OFF First Year",
      discount: "30% OFF",
      description: "Learn languages with the world's #1 language learning app. Get Plus features at a discounted rate.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/duolingo-plus.png",
      begin_date: "2025-04-10",
      end_date: "2025-06-10",
      origin_url: "https://www.duolingo.com/",
      tracking_url: "https://www.linkbux.com/track/duolingo-plus-discount",
      category: "Education"
    },
    {
      id: 13,
      deal_name: "O'Reilly Learning: 50% OFF First 3 Months",
      discount: "50% OFF",
      description: "Get unlimited access to tech & business books, videos, and interactive learning experiences.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/oreilly-learning.png",
      begin_date: "2025-04-20",
      end_date: "2025-05-20",
      origin_url: "https://www.oreilly.com/",
      tracking_url: "https://www.linkbux.com/track/oreilly-learning-discount",
      category: "Education"
    },
    {
      id: 14,
      deal_name: "edX Professional Certificate: 25% OFF",
      discount: "25% OFF",
      description: "Gain job-relevant skills from leading universities and companies with edX Professional Certificates.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/edx-certificates.png",
      begin_date: "2025-04-05",
      end_date: "2025-05-31",
      origin_url: "https://www.edx.org/",
      tracking_url: "https://www.linkbux.com/track/edx-certificate-discount",
      category: "Education"
    },
    {
      id: 15,
      deal_name: "Pluralsight Skills: 40% OFF Annual Subscription",
      discount: "40% OFF",
      description: "Build in-demand tech skills with expert-led courses, assessments, and hands-on learning paths.",
      img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/pluralsight-skills.png",
      begin_date: "2025-04-25",
      end_date: "2025-05-25",
      origin_url: "https://www.pluralsight.com/",
      tracking_url: "https://www.linkbux.com/track/pluralsight-annual-discount",
      category: "Education"
    }
  ];
  const calculateRemainingDays = (endDate) => {
    const end = new Date(endDate);
    const now = /* @__PURE__ */ new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1e3 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };
  const dealsWithDaysRemaining = deals.map((deal) => {
    const daysRemaining = calculateRemainingDays(deal.end_date);
    return {
      ...deal,
      daysRemaining
    };
  });
  const textDeals = dealsWithDaysRemaining.slice(0, 9);
  const imageDeals = dealsWithDaysRemaining.slice(9, 15);
  return renderTemplate`${maybeRenderHead()}<section class="py-8 sm:py-12 bg-gray-50"> <div${addAttribute(`container mx-auto px-3 sm:px-4 ${containerWidth}`, "class")}> <div class="text-center mb-8"> <span class="inline-block px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full mb-3">Hot Deals</span> <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-3">Today's Best Offers</h2> <p class="text-lg text-gray-600 max-w-3xl mx-auto">Don't miss these limited-time deals on educational resources and courses.</p> </div> <div class="grid grid-cols-12 gap-4"> <!-- 左侧：9个纯文本卡片 --> <div class="col-span-12 lg:col-span-6"> <div class="grid grid-cols-1 gap-3 h-full"> ${textDeals.map((deal) => renderTemplate`<a${addAttribute(deal.tracking_url, "href")} target="_blank" rel="noopener" class="bg-white rounded-lg shadow-sm p-3 hover:shadow-md transition-all hover:bg-purple-50 border border-gray-100 block group"> <div class="flex justify-between items-start mb-1.5"> <h3 class="text-sm font-medium text-gray-900 group-hover:text-purple-700 transition-colors pr-2">${deal.deal_name}</h3> <span class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full whitespace-nowrap flex-shrink-0">${deal.discount}</span> </div> <p class="text-xs text-gray-600">${deal.description}</p> ${deal.daysRemaining > 0 && deal.daysRemaining <= 30 && renderTemplate`<span class="inline-block px-2 py-0.5 bg-orange-100 text-orange-800 text-xs rounded mt-2">${deal.daysRemaining} days left</span>`} </a>`)} </div> </div> <!-- 右侧：6个中型卡片（带图片），2列排列 --> <div class="col-span-12 lg:col-span-6"> <div class="grid grid-cols-1 md:grid-cols-2 gap-3 h-full"> ${imageDeals.map((deal) => renderTemplate`<a${addAttribute(deal.tracking_url, "href")} target="_blank" rel="noopener" class="bg-white rounded-lg shadow-md overflow-hidden transition-all hover:shadow-lg hover:scale-[1.01] border border-gray-100 block group h-full"> <div class="relative aspect-square"> <img${addAttribute(deal.img, "src")}${addAttribute(deal.deal_name, "alt")} class="w-full h-full object-cover"> <div class="absolute top-2 right-2 bg-purple-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">${deal.discount}</div> <div class="absolute bottom-0 left-0 bg-gradient-to-t from-black/70 to-transparent w-full h-10"></div> <div class="absolute bottom-2 left-2"> <span class="inline-block px-2 py-0.5 bg-white/20 backdrop-blur-sm text-white text-xs rounded">${deal.category}</span> </div> </div> <div class="p-3 pt-3.5"> <h3 class="text-sm font-semibold text-gray-900 group-hover:text-purple-700 transition-colors mb-1">${deal.deal_name}</h3> <p class="text-xs text-gray-600">${deal.description}</p> <div class="flex justify-between items-center mt-2"> <span class="text-xs text-gray-500"> ${new Date(deal.begin_date).toLocaleDateString("en-US", { month: "short", day: "numeric" })} </span> ${deal.daysRemaining > 0 && deal.daysRemaining <= 30 && renderTemplate`<span class="text-xs bg-orange-100 text-orange-800 px-1.5 py-0.5 rounded-sm">${deal.daysRemaining}d left</span>`} </div> </div> </a>`)} </div> </div> </div> <div class="text-center mt-8"> <a href="/deals" class="inline-flex items-center text-purple-600 hover:text-purple-800 font-medium">
View All Deals
<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> </div> </section>`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/components/DealsSection.astro", void 0);

const $$Astro$1 = createAstro("https://smartreviews.com");
const $$TagList = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$TagList;
  const {
    tags = [],
    title,
    showCount = false,
    className = "",
    compact = false
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(className, "class")}> ${title && renderTemplate`<h3 class="text-xl font-bold mb-4">${title}</h3>`} ${tags.length > 0 ? renderTemplate`<div${addAttribute(`flex flex-wrap ${compact ? "gap-1" : "gap-2"}`, "class")}> ${tags.map((tag) => renderTemplate`<a${addAttribute(`/articles?tag=${tag.slug}`, "href")}${addAttribute(`
            ${compact ? "text-xs px-2 py-1" : "text-sm px-4 py-2"} 
            bg-white border border-gray-200 hover:border-purple-300 
            text-gray-700 hover:text-purple-700 rounded-full 
            font-medium transition-colors whitespace-nowrap
          `, "class")}>
# ${tag.name} ${showCount && tag.id && renderTemplate`<span class="ml-1 text-xs text-gray-400">(id:${tag.id})</span>`} </a>`)} </div>` : renderTemplate`<div class="py-3"> <p class="text-gray-500 text-sm">No tags available</p> </div>`} </div>`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/components/TagList.astro", void 0);

const $$Index$2 = createComponent(async ($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  const featuredArticles = [
    {
      id: 1,
      title: "Top 10 Wireless Headphones for 2023",
      excerpt: "Discover the best wireless headphones that combine superior sound quality, comfort, and battery life for an exceptional listening experience.",
      image: "/images/headphones.jpg",
      category: "Audio",
      brand: "Various",
      publishedDate: "October 15, 2023",
      author: "John Smith",
      slug: "top-10-wireless-headphones-2023"
    },
    {
      id: 2,
      title: "The Ultimate Guide to Choosing a Gaming Laptop",
      excerpt: "Everything you need to know about selecting the perfect gaming laptop that meets your performance needs and budget constraints.",
      image: "/images/gaming-laptop.jpg",
      category: "Computers",
      brand: "Various",
      publishedDate: "October 10, 2023",
      author: "Emily Johnson",
      slug: "ultimate-guide-gaming-laptop"
    },
    {
      id: 3,
      title: "Smart Home Devices That Actually Save You Money",
      excerpt: "These smart home gadgets not only make your life more convenient but also help reduce your monthly bills significantly.",
      image: "/images/smart-home.jpg",
      category: "Smart Home",
      brand: "Various",
      publishedDate: "October 5, 2023",
      author: "Michael Brown",
      slug: "smart-home-devices-save-money"
    }
  ];
  const popularBrands = [
    { id: 1, name: "Apple", logo: "/images/apple-logo.svg", slug: "apple" },
    { id: 2, name: "Samsung", logo: "/images/samsung-logo.svg", slug: "samsung" },
    { id: 3, name: "Sony", logo: "/images/sony-logo.svg", slug: "sony" },
    { id: 4, name: "Microsoft", logo: "/images/microsoft-logo.svg", slug: "microsoft" },
    { id: 5, name: "Amazon", logo: "/images/amazon-logo.svg", slug: "amazon" },
    { id: 6, name: "Google", logo: "/images/google-logo.svg", slug: "google" }
  ];
  let categories = [];
  try {
    const categoryData = await Api.Category.getCategoryList({ featured: true, page_size: 6 });
    categories = categoryData.category_list;
  } catch (error) {
    console.error("\u83B7\u53D6\u5206\u7C7B\u6570\u636E\u5931\u8D25:", error);
    categories = [];
  }
  const recentArticles = [
    {
      id: 4,
      title: "Best Budget Smartphones Under $300",
      excerpt: "Quality smartphones don't have to break the bank. Here are our top picks for budget-friendly options in 2023.",
      image: "/images/budget-smartphone.jpg",
      category: "Smartphones",
      brand: "Various",
      publishedDate: "October 1, 2023",
      author: "Sarah Wilson",
      slug: "best-budget-smartphones-under-300"
    },
    {
      id: 5,
      title: "Mechanical Keyboards: Are They Worth the Hype?",
      excerpt: "We dive deep into the world of mechanical keyboards to help you decide if they're worth the investment for your typing needs.",
      image: "/images/mechanical-keyboard.jpg",
      category: "Peripherals",
      brand: "Various",
      publishedDate: "September 28, 2023",
      author: "David Lee",
      slug: "mechanical-keyboards-worth-the-hype"
    },
    {
      id: 6,
      title: "The Best Fitness Trackers for Every Budget",
      excerpt: "From basic step counters to advanced health monitors, find the perfect fitness tracker to help you achieve your wellness goals.",
      image: "/images/fitness-trackers.jpg",
      category: "Wearables",
      brand: "Various",
      publishedDate: "September 25, 2023",
      author: "Jennifer Martinez",
      slug: "best-fitness-trackers-every-budget"
    },
    {
      id: 7,
      title: "How to Choose the Right 4K TV in 2023",
      excerpt: "Navigate the complex world of 4K TVs with our comprehensive guide to finding the perfect display for your home entertainment setup.",
      image: "/images/4k-tv.jpg",
      category: "TVs",
      brand: "Various",
      publishedDate: "September 20, 2023",
      author: "Robert Taylor",
      slug: "how-to-choose-right-4k-tv-2023"
    }
  ];
  let popularTags = [];
  try {
    console.log("Fetching featured tags...");
    const featuredTagsResponse = await Api.Tag.getTagList({
      page: 1,
      page_size: 10,
      featured: true
    });
    popularTags = featuredTagsResponse.tag_list || [];
    console.log(`Successfully retrieved ${popularTags.length} featured tags`);
  } catch (error) {
    console.error("Failed to fetch tags:", error);
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "SmartReviews - Insightful Articles & Exclusive Deals", "description": "Discover insightful articles, helpful guides, and exclusive deals on the latest tech products and consumer goods." }, { "default": async ($$result2) => renderTemplate`  ${renderComponent($$result2, "Hero", $$Hero, {})}  ${maybeRenderHead()}<section class="py-16 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden"> <!-- Decorative elements --> <div class="absolute top-1/4 right-0 w-72 h-72 bg-purple-50 rounded-full opacity-70 blur-3xl"></div> <div class="absolute bottom-0 left-1/4 w-64 h-64 bg-purple-100 rounded-full opacity-40 blur-3xl"></div> <div${addAttribute(`container mx-auto px-4 ${containerWidth} relative`, "class")}> <div class="flex flex-col sm:flex-row justify-between items-center mb-12"> <div> <span class="inline-block px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full mb-2 shadow-sm">Curated Collection</span> <h2 class="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-800 to-purple-600">Featured Articles</h2> </div> <a href="/articles" class="mt-4 sm:mt-0 group text-purple-600 hover:text-purple-800 font-medium flex items-center transition-all duration-300 hover:translate-x-1">
View all
<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8"> ${featuredArticles.map((article, index) => renderTemplate`<article class="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 h-full flex flex-col hover:shadow-xl hover:shadow-purple-100/50 transform hover:scale-[1.01] transition-all duration-500 group"> <div class="relative pb-[60%] overflow-hidden"> <img${addAttribute(article.image, "src")}${addAttribute(article.title, "alt")} class="absolute inset-0 w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"> <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div> <span class="absolute top-4 left-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium px-3 py-1 rounded-full shadow-md"> ${article.category} </span> </div> <div class="p-6 md:p-8 flex-grow flex flex-col"> <h3 class="text-xl font-bold mb-3 line-clamp-2"> <a${addAttribute(`/article/${article.slug}`, "href")} class="text-gray-900 hover:text-purple-600 transition-colors"> ${article.title} </a> </h3> <p class="text-gray-600 mb-6 flex-grow">${article.excerpt}</p> <div class="flex justify-between items-center text-sm"> <span class="text-gray-500">${article.publishedDate}</span> <span class="flex items-center"> <img${addAttribute(`https://randomuser.me/api/portraits/${index % 2 === 0 ? "men" : "women"}/${(index + 1) * 10}.jpg`, "src")}${addAttribute(article.author, "alt")} class="w-6 h-6 rounded-full mr-2 border border-purple-200"> <span class="text-gray-700 font-medium">${article.author}</span> </span> </div> </div> </article>`)} </div> </div> </section>  <section class="py-16 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="flex justify-between items-center mb-8"> <h2 class="text-3xl font-bold">Popular Brands</h2> <a href="/brands" class="text-purple-600 hover:text-purple-800 font-medium flex items-center">
View all
<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6"> ${popularBrands.map((brand) => renderTemplate`<a${addAttribute(`/brand/${brand.slug}`, "href")} class="bg-white rounded-lg shadow-sm p-6 flex items-center justify-center hover:shadow-md transition-shadow"> <img${addAttribute(brand.logo, "src")}${addAttribute(`${brand.name} logo`, "alt")} class="h-12 max-w-full object-contain"> </a>`)} </div> </div> </section>  ${renderComponent($$result2, "DealsSection", $$DealsSection, {})}  <section class="py-16 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="flex justify-between items-center mb-8"> <h2 class="text-3xl font-bold">Categories</h2> <a href="/category" class="text-purple-600 hover:text-purple-800 font-medium flex items-center">
View all
<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 gap-4"> ${categories.map((category) => renderTemplate`<a${addAttribute(`/category/${category.slug}`, "href")} class="bg-white rounded-lg shadow-sm p-5 text-center hover:shadow-md transition-shadow flex flex-col justify-center items-center h-full"> <h3 class="font-bold mb-1 text-sm sm:text-base truncate w-full">${category.name}</h3> <p class="text-xs sm:text-sm text-gray-500">${category.description ? category.description.substring(0, 50) + "..." : ""}</p> </a>`)} </div> </div> </section>  <section class="py-16 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="flex flex-col sm:flex-row justify-between items-center mb-10"> <div> <span class="inline-block px-3 py-1 text-sm font-medium text-purple-800 bg-purple-100 rounded-full mb-2 shadow-sm">Latest Updates</span> <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Recent Articles</h2> </div> <a href="/articles" class="mt-4 sm:mt-0 group text-purple-600 hover:text-purple-800 font-medium flex items-center transition-all duration-300 hover:translate-x-1">
View all
<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> ${recentArticles.map((article, index) => renderTemplate`<article class="flex border-b border-gray-100 pb-8 last:border-0 last:pb-0 group"> <div class="w-24 h-24 sm:w-28 sm:h-28 flex-shrink-0 rounded-lg overflow-hidden"> <img${addAttribute(article.image, "src")}${addAttribute(article.title, "alt")} class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"> </div> <div class="ml-4 flex-1"> <div class="flex items-center mb-2"> <span class="text-xs font-medium px-2 py-1 bg-purple-100 text-purple-800 rounded-full shadow-sm">${article.category}</span> <span class="ml-auto text-xs text-gray-500">${article.publishedDate}</span> </div> <h3 class="text-lg font-bold mb-1"> <a${addAttribute(`/article/${article.slug}`, "href")} class="text-gray-900 hover:text-purple-600 transition-colors"> ${article.title} </a> </h3> <p class="text-sm text-gray-600 line-clamp-2">${article.excerpt}</p> </div> </article>`)} </div> </div> </section>  <section class="py-16 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="flex justify-between items-center mb-8"> <h2 class="text-3xl font-bold">Popular Tags</h2> <a href="/tags" class="text-purple-600 hover:text-purple-800 font-medium flex items-center">
View all
<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </a> </div> ${renderComponent($$result2, "TagList", $$TagList, { "tags": popularTags, "className": "mt-4" })} </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center`, "class")}> <h2 class="text-3xl md:text-4xl font-bold mb-6">Subscribe to Our Newsletter</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">Get the latest reviews, guides and exclusive deals delivered directly to your inbox.</p> <form class="max-w-xl mx-auto flex flex-col sm:flex-row gap-4"> <input type="email" placeholder="Your email address" class="flex-grow px-4 py-3 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-300 text-gray-900" required> <button type="submit" class="bg-purple-600 hover:bg-purple-500 text-white px-6 py-3 rounded-md font-medium whitespace-nowrap transition-colors">
Subscribe
</button> </form> <p class="mt-4 text-sm text-purple-200">By subscribing, you agree to our Privacy Policy and consent to receive updates from us.</p> </div> </section>  ${renderComponent($$result2, "FeaturedCarousel", $$FeaturedCarousel, {})} ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/index.astro", void 0);

const $$file$2 = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/index.astro";
const $$url$2 = "";

const index$2 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index$2,
  file: $$file$2,
  url: $$url$2
}, Symbol.toStringTag, { value: 'Module' }));

const $$Index$1 = createComponent(async ($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  let categories = [];
  try {
    const categoryData = await Api.Category.getCategoryList({ page_size: 100 });
    categories = categoryData.category_list;
  } catch (error) {
    console.error("Failed to get category data:", error);
    categories = [];
  }
  let mainCategories = [];
  try {
    const featuredCategoryData = await Api.Category.getCategoryList({ featured: true, page_size: 6 });
    mainCategories = featuredCategoryData.category_list;
  } catch (error) {
    console.error("Failed to get featured category data:", error);
    mainCategories = categories.slice(0, 6);
  }
  function getCategoryIcon(iconName) {
    const normalizedIconName = (iconName || "").toLowerCase().trim();
    switch (normalizedIconName) {
      case "smartphone":
      case "smartphones":
      case "phone":
      case "mobile":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>`;
      case "laptop":
      case "laptops":
      case "computer":
      case "computers":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>`;
      case "headphone":
      case "headphones":
      case "audio":
      case "sound":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
      </svg>`;
      case "home":
      case "smarthome":
      case "smart home":
      case "house":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
      </svg>`;
      case "watch":
      case "watches":
      case "smartwatch":
      case "wearable":
      case "wearables":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`;
      case "camera":
      case "cameras":
      case "photo":
      case "photography":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>`;
      case "tv":
      case "television":
      case "monitor":
      case "display":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>`;
      case "game":
      case "gaming":
      case "gamepad":
      case "console":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`;
      case "keyboard":
      case "keyboards":
      case "input":
      case "peripherals":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>`;
      case "tablet":
      case "tablets":
      case "ipad":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>`;
      case "usb":
      case "accessory":
      case "accessories":
      case "cable":
      case "cables":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
      </svg>`;
      case "code":
      case "software":
      case "app":
      case "apps":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>`;
      case "book":
      case "books":
      case "ebook":
      case "reading":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
      </svg>`;
      case "briefcase":
      case "business":
      case "work":
      case "office":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>`;
      case "theater-masks":
      case "entertainment":
      case "theater":
      case "theatre":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`;
      case "car":
      case "cars":
      case "vehicle":
      case "automotive":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`;
      case "food":
      case "kitchen":
      case "cooking":
      case "appliance":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
      </svg>`;
      case "fitness":
      case "health":
      case "exercise":
      case "workout":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>`;
      case "travel":
      case "luggage":
      case "vacation":
      case "tourism":
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
      </svg>`;
      default:
        return `<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`;
    }
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Product Categories | Best Tech Reviews & Buying Guides | SmartReviews", "description": "Explore our comprehensive product categories for in-depth tech reviews, expert comparisons, and exclusive buying guides to make informed purchase decisions." }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<section class="bg-gradient-to-r from-purple-800 to-indigo-900 text-white py-20 relative overflow-hidden"> <!-- Decorative elements for visual interest --> <div class="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-purple-600 to-transparent opacity-20"></div> <div class="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-black to-transparent opacity-20"></div> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center relative z-10`, "class")}> <div class="max-w-4xl mx-auto"> <div class="inline-block bg-white bg-opacity-10 p-4 rounded-full mb-8 backdrop-blur-sm shadow-lg transform transition-transform duration-500 hover:scale-110"> <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path> </svg> </div> <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight animate-fadeIn">Product Categories</h1> <p class="text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed mb-8 text-gray-100">Discover expert reviews and comprehensive buying guides across all tech categories to make informed purchase decisions.</p> <div class="flex flex-wrap justify-center gap-4 mt-8"> <a href="#featured" class="bg-white text-purple-800 hover:bg-purple-50 px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center"> <span>Featured Categories</span> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path> </svg> </a> <a href="#all" class="bg-purple-600 bg-opacity-30 backdrop-blur-sm text-white hover:bg-opacity-50 hover:text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 border border-white border-opacity-20">
Browse All Categories
</a> </div> </div> </div> </section>  <section id="featured" class="py-16 bg-white relative overflow-hidden"> <!-- Decorative elements --> <div class="absolute top-0 left-0 w-64 h-64 bg-purple-50 rounded-full opacity-50 blur-3xl"></div> <div class="absolute bottom-0 right-0 w-96 h-96 bg-indigo-50 rounded-full opacity-40 blur-3xl"></div> <div${addAttribute(`container mx-auto px-4 ${containerWidth} relative z-10`, "class")}> <div class="text-center mb-12"> <span class="inline-block px-4 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-semibold tracking-wide mb-3 shadow-sm">HANDPICKED FOR YOU</span> <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900 flex items-center justify-center"> <span class="bg-purple-100 text-purple-700 p-3 rounded-lg mr-3 shadow-md"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path> </svg> </span>
Featured Categories
</h2> <p class="text-gray-600 max-w-2xl mx-auto text-lg">Our most popular product categories with expert reviews and buying guides</p> </div> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"> ${mainCategories.map((category, index) => renderTemplate`<a${addAttribute(`/articles?category=${category.slug}`, "href")} class="group"> <div class="card overflow-hidden h-full flex flex-col hover:shadow-xl transition-all duration-500 rounded-2xl border border-gray-100 transform group-hover:-translate-y-2 bg-white"${addAttribute(`animation-delay: ${index * 100}ms`, "style")}> <div class="bg-gradient-to-br from-purple-50 via-white to-indigo-50 p-8 flex flex-col items-center relative h-full">  <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-purple-100 to-transparent opacity-70 rounded-bl-full"></div> <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg mb-6 group-hover:scale-110 transition-transform duration-500 border border-purple-100 relative z-10"> <div class="text-purple-700"> ${renderComponent($$result2, "Fragment", Fragment, {}, { "default": async ($$result3) => renderTemplate`${unescapeHTML(getCategoryIcon(category.icon))}` })} </div> </div> <h3 class="text-2xl font-bold text-gray-900 mb-3 text-center group-hover:text-purple-700 transition-colors">${category.name}</h3> <p class="text-gray-600 text-center">${category.description}</p> <div class="mt-6 bg-gradient-to-r from-purple-600 to-purple-700 text-white px-6 py-3 rounded-full font-medium group-hover:from-purple-700 group-hover:to-indigo-700 transition-all duration-300 inline-flex items-center shadow-md"> <span>Browse Reviews</span> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </div> </div> </div> </a>`)} </div> <div class="text-center mt-12"> <a href="#all" class="inline-flex items-center text-purple-700 hover:text-purple-900 font-medium"> <span>View All Categories</span> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path> </svg> </a> </div> </div> </section>  <section id="all" class="py-16 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden"> <!-- Decorative elements --> <div class="absolute top-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-200 to-transparent"></div> <div class="absolute bottom-0 right-0 w-72 h-72 bg-indigo-50 rounded-full opacity-30 blur-3xl -z-10"></div> <div${addAttribute(`container mx-auto px-4 ${containerWidth} relative z-10`, "class")}> <div class="text-center mb-12"> <span class="inline-block px-4 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm font-semibold tracking-wide mb-3 shadow-sm">COMPREHENSIVE COLLECTION</span> <h2 class="text-3xl md:text-4xl font-bold mb-4 text-gray-900 flex items-center justify-center"> <span class="bg-indigo-100 text-indigo-700 p-3 rounded-lg mr-3 shadow-md"> <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path> </svg> </span>
All Categories
</h2> <p class="text-gray-600 max-w-2xl mx-auto text-lg mb-8">Browse our complete collection of product categories to find detailed reviews and buying guides</p> </div> <!-- Category Filter (optional enhancement) --> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${categories.map((category, index) => renderTemplate`<a${addAttribute(`/articles?category=${category.slug}`, "href")} class="group"${addAttribute(`animation-delay: ${index * 50}ms`, "style")}> <div class="card p-6 hover:shadow-lg transition-all duration-300 h-full flex flex-col bg-white rounded-xl border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-1 hover:bg-gradient-to-br hover:from-white hover:to-indigo-50"> <div class="flex flex-col items-center text-center mb-4"> <div class="flex items-center gap-3 mb-3"> <div class="w-14 h-14 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm transition-all duration-300 group-hover:scale-110 group-hover:shadow-md group-hover:bg-gradient-to-br group-hover:from-indigo-100 group-hover:to-purple-100 border border-indigo-100"> <div class="text-indigo-700"> ${renderComponent($$result2, "Fragment", Fragment, {}, { "default": async ($$result3) => renderTemplate`${unescapeHTML(getCategoryIcon(category.icon))}` })} </div> </div> <h3 class="text-lg font-bold text-gray-900 group-hover:text-indigo-700 transition-colors">${category.name}</h3> </div> <p class="text-gray-600 text-sm">${category.description}</p> </div> <div class="text-indigo-600 font-medium flex items-center mt-auto text-sm justify-center group-hover:text-indigo-800 transition-colors"> <span>View Reviews</span> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </div> </div> </a>`)} </div> </div> </section>  <section class="py-20 bg-gradient-to-r from-indigo-800 to-purple-900 text-white relative overflow-hidden"> <!-- Decorative elements --> <div class="absolute inset-0 bg-pattern opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=" 20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" %3E%3Cg fill="%23ffffff" fill-opacity="0.3" fill-rule="evenodd" %3E%3Ccircle cx="3" cy="3" r="3" %3E%3Ccircle cx="13" cy="13" r="3" %3E%3C g%3E%3C svg%3E')"></div> <div class="absolute top-0 left-0 w-full h-24 bg-gradient-to-b from-white to-transparent opacity-10"></div> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center relative z-10`, "class")}> <div class="max-w-4xl mx-auto"> <div class="inline-block bg-white bg-opacity-10 p-3 rounded-full mb-8 backdrop-blur-sm"> <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path> </svg> </div> <h2 class="text-3xl md:text-5xl font-bold mb-6 leading-tight">Can't Find the Category You're Looking For?</h2> <p class="text-xl mb-10 max-w-3xl mx-auto text-indigo-100">Tell us what products you want to learn about, and our expert team will provide detailed reviews and personalized recommendations.</p> <div class="flex flex-wrap justify-center gap-4"> <a href="/contact" class="inline-flex items-center bg-white text-indigo-700 hover:bg-indigo-50 font-medium px-8 py-4 rounded-full transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"> <span>Contact Our Team</span> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path> </svg> </a> <a href="/submit-request" class="inline-flex items-center bg-transparent text-white border border-white border-opacity-30 hover:bg-white hover:bg-opacity-10 font-medium px-8 py-4 rounded-full transition-all duration-300 backdrop-blur-sm"> <span>Submit Request</span> </a> </div> </div> </div> </section> ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/category/index.astro", void 0);

const $$file$1 = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/category/index.astro";
const $$url$1 = "/category";

const index$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index$1,
  file: $$file$1,
  url: $$url$1
}, Symbol.toStringTag, { value: 'Module' }));

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro = createAstro("https://smartreviews.com");
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const rawURL = Astro2.url.toString();
  console.log("Raw URL: ", rawURL);
  const pageSize = 50;
  const search = Astro2.url.searchParams.get("search") || "";
  let allTags = [];
  let featuredTags = [];
  let totalTags = 0;
  let errorMessage = "";
  try {
    const [tagsResponse, featuredResponse] = await Promise.all([
      Api.Tag.getTagList({
        page: 1,
        page_size: pageSize,
        search: search || void 0
      }),
      Api.Tag.getTagList({
        page: 1,
        page_size: 10,
        featured: true
      })
    ]);
    allTags = tagsResponse.tag_list || [];
    totalTags = tagsResponse.total || 0;
    featuredTags = featuredResponse.tag_list || [];
  } catch (error) {
    console.error("Error fetching tags:", error);
    errorMessage = "Unable to load tags. Please try again later.";
  }
  const totalPages = Math.ceil(totalTags / pageSize);
  const pageTitle = search ? `Search Results for "${search}"` : "All Tags";
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": `Browse Tags`, "description": "Browse all tags on SmartReviews and find content that interests you." }, { "default": async ($$result2) => renderTemplate(_a || (_a = __template(["  ", '<section class="py-12 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h1 class="text-4xl md:text-5xl font-bold mb-4">Browse Tags</h1> <p class="text-xl max-w-3xl mx-auto">Find relevant content through tags to help you make informed decisions.</p> </div> </section>  <section class="py-8 bg-gray-50 border-b border-gray-200"> <div class="container mx-auto px-4 max-w-5xl"> <form class="max-w-2xl mx-auto" action="/tags" method="get" id="search-form"> <div class="relative"> <input type="text" name="search"', ' placeholder="Search tags..." class="w-full pl-4 pr-14 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"> <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-md transition-colors"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </button> </div> </form> </div> </section>  <section class="py-12 bg-white"> <div class="container mx-auto px-4 max-w-5xl"> ', ' </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h2 class="text-3xl md:text-4xl font-bold mb-6">Looking for Something Specific?</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">Browse our complete article collection to discover more content.</p> <div class="flex flex-wrap justify-center gap-4"> <a href="/articles" class="bg-white text-purple-700 hover:bg-gray-100 font-medium py-3 px-6 rounded-lg transition-colors">\nBrowse All Articles\n</a> <a href="/category" class="bg-purple-600 text-white hover:bg-purple-700 font-medium py-3 px-6 rounded-lg transition-colors border border-purple-500">\nBrowse Categories\n</a> </div> </div> </section>  <script>(function(){', `
    // Configuration information
    const API_BASE_URL = 'http://127.0.0.1:9091';
    const API_VERSION = 'v1';
    
    // Current page information
    let currentPage = 1;
    let searchQuery = '';
    
    // Function to get URL parameters
    function getUrlParams() {
      const urlParams = new URLSearchParams(window.location.search);
      const pageParam = urlParams.get('page');
      const searchParam = urlParams.get('search') || '';
      
      currentPage = pageParam ? parseInt(pageParam) : 1;
      if (isNaN(currentPage) || currentPage < 1) currentPage = 1;
      
      searchQuery = searchParam;
      
      console.log('Client-side detected page:', currentPage, 'search:', searchQuery);
      return { currentPage, searchQuery };
    }
    
    // Update UI function
    function updatePageUI(page, search, totalPages, totalTags) {
      // Update page title
      const pageTitle = document.getElementById('page-title');
      if (pageTitle) {
        pageTitle.textContent = search 
          ? \`Search Results for "\${search}"\` 
          : page > 1 
            ? \`All Tags - Page \${page}\` 
            : "All Tags";
      }
      
      // Hide/show featured tags
      const featuredContainer = document.getElementById('featured-tags-container');
      if (featuredContainer) {
        featuredContainer.style.display = (page > 1 || search) ? 'none' : 'block';
      }
      
      // Update page number information
      const paginationInfo = document.getElementById('pagination-info');
      const paginationTotalPages = document.getElementById('pagination-total-pages');
      if (paginationInfo) {
        const pageNumSpan = paginationInfo.querySelector('span.font-medium');
        if (pageNumSpan) pageNumSpan.textContent = page.toString();
        if (paginationTotalPages) paginationTotalPages.textContent = totalPages.toString();
      }
      
      // Show/hide pagination container
      const paginationContainer = document.getElementById('pagination-container');
      if (paginationContainer) {
        paginationContainer.style.display = totalPages > 1 ? 'block' : 'none';
      }
    }
    
    // Update pagination controls - Enhanced version
    function updatePagination(currentPage, totalPages) {
      const paginationElement = document.getElementById('pagination-controls');
      if (!paginationElement || totalPages <= 1) return;
      
      // Clear existing pagination
      paginationElement.innerHTML = '';
      
      // Function to generate pagination URL
      function getPaginationUrl(page) {
        const params = new URLSearchParams();
        if (searchQuery) params.set('search', searchQuery);
        if (page > 1) params.set('page', page.toString());
        return \`/tags\${params.toString() ? \`?\${params.toString()}\` : ''}\`;
      }
      
      // Calculate pagination range
      function getPaginationRange(current, total) {
        let start = Math.max(1, current - 1);
        let end = Math.min(total, start + 2);
        
        if (end - start < 2 && total > 2) {
          start = Math.max(1, end - 2);
        }
        
        // Ensure at least the first three pages are displayed
        if (current <= 3) {
          start = 1;
          end = Math.min(total, 3);
        }
        
        // Ensure at least the last few pages are displayed
        if (current >= total - 2) {
          end = total;
          start = Math.max(1, total - 2);
        }
        
        return Array.from({ length: end - start + 1 }, (_, i) => start + i);
      }

      // Create pagination button
      function createPaginationButton(text, href, isActive = false, isDisabled = false, ariaLabel = '') {
        const button = document.createElement(isDisabled ? 'span' : 'a');
        if (!isDisabled) button.href = href;
        
        // Basic style
        let className = 'inline-flex items-center justify-center min-w-[36px] h-9 px-2 mx-0.5 text-sm font-medium transition-colors ';
        
        // Status style
        if (isActive) {
          className += 'text-white bg-purple-600 rounded-md shadow-sm';
        } else if (isDisabled) {
          className += 'text-gray-400 cursor-not-allowed bg-gray-50 rounded-md';
        } else {
          className += 'text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-md';
        }
        
        button.className = className;
        
        if (ariaLabel) button.setAttribute('aria-label', ariaLabel);
        if (isActive) button.setAttribute('aria-current', 'page');
        
        button.innerHTML = text;
        return button;
      }
      
      // First page button
      if (totalPages > 3) {
        const firstButton = createPaginationButton(
          '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" /><path fill-rule="evenodd" d="M9.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" /></svg>',
          getPaginationUrl(1),
          false,
          currentPage === 1,
          'Go to first page'
        );
        paginationElement.appendChild(firstButton);
      }
      
      // Previous page button
      const prevButton = createPaginationButton(
        '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>',
        getPaginationUrl(currentPage - 1),
        false,
        currentPage === 1,
        'Previous page'
      );
      paginationElement.appendChild(prevButton);
      
      // Page number buttons
      const paginationPages = getPaginationRange(currentPage, totalPages);
      
      // If the range doesn't start from 1, add ellipsis
      if (paginationPages[0] > 1) {
        if (paginationPages[0] > 2) {
          // If the distance is far, add "1" button and ellipsis
          const firstPageButton = createPaginationButton('1', getPaginationUrl(1));
          paginationElement.appendChild(firstPageButton);
          
          const ellipsis = createPaginationButton('...', '#', false, true);
          paginationElement.appendChild(ellipsis);
        } else {
          // If only difference is 1, directly add "1" button
          const firstPageButton = createPaginationButton('1', getPaginationUrl(1));
          paginationElement.appendChild(firstPageButton);
        }
      }
      
      // Add page number buttons
      paginationPages.forEach(pageNum => {
        const pageButton = createPaginationButton(
          pageNum.toString(),
          getPaginationUrl(pageNum),
          pageNum === currentPage,
          false,
          \`Page \${pageNum}\`
        );
        paginationElement.appendChild(pageButton);
      });
      
      // If the range doesn't reach the last page, add ellipsis
      if (paginationPages[paginationPages.length - 1] < totalPages) {
        if (paginationPages[paginationPages.length - 1] < totalPages - 1) {
          // If the distance is far, add ellipsis and last page button
          const ellipsis = createPaginationButton('...', '#', false, true);
          paginationElement.appendChild(ellipsis);
          
          const lastPageButton = createPaginationButton(totalPages.toString(), getPaginationUrl(totalPages));
          paginationElement.appendChild(lastPageButton);
        } else {
          // If only difference is 1, directly add last page button
          const lastPageButton = createPaginationButton(totalPages.toString(), getPaginationUrl(totalPages));
          paginationElement.appendChild(lastPageButton);
        }
      }
      
      // Next page button
      const nextButton = createPaginationButton(
        '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>',
        getPaginationUrl(currentPage + 1),
        false,
        currentPage === totalPages,
        'Next page'
      );
      paginationElement.appendChild(nextButton);
      
      // Last page button
      if (totalPages > 3) {
        const lastButton = createPaginationButton(
          '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" /><path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>',
          getPaginationUrl(totalPages),
          false,
          currentPage === totalPages,
          'Go to last page'
        );
        paginationElement.appendChild(lastButton);
      }
    }
    
    // Get tag data
    async function fetchTags(page, search) {
      try {
        document.getElementById('tags-list').innerHTML = '<p class="text-center w-full py-8">Loading page data...</p>';
        
        // Build API URL
        const queryParams = new URLSearchParams();
        queryParams.set('page', page.toString());
        queryParams.set('page_size', pageSize.toString());
        if (search) queryParams.set('search', search);
        
        const apiUrl = \`\${API_BASE_URL}/api/\${API_VERSION}/tag?\${queryParams.toString()}\`;
        console.log('Fetching tags from:', apiUrl);
        
        // Get data
        const response = await fetch(apiUrl);
        const data = await response.json();
        
        if (data && data.code === 200 && data.data && data.data.tag_list) {
          const tags = data.data.tag_list;
          console.log(\`Fetched \${tags.length} tags for page \${page}\`);
          
          // No longer update removed count display
          
          // Update total pages
          const newTotalPages = Math.ceil(data.data.total / pageSize);
          
          // Render tags
          const tagsListElement = document.getElementById('tags-list');
          if (tagsListElement) {
            if (tags.length === 0) {
              tagsListElement.innerHTML = '<p class="text-center w-full py-4 text-gray-600">No tags found for this page.</p>';
            } else {
              tagsListElement.innerHTML = '';
              
              // Create tag elements
              tags.forEach(tag => {
                const tagElement = document.createElement('a');
                tagElement.href = \`/articles?tag=\${tag.slug}\`;
                tagElement.className = "text-sm px-4 py-2 bg-white border border-gray-200 hover:border-purple-300 text-gray-700 hover:text-purple-700 rounded-full font-medium transition-colors whitespace-nowrap";
                // Add # prefix to tag name
                tagElement.textContent = \`# \${tag.name}\`;
                tagsListElement.appendChild(tagElement);
              });
            }
          }
          
          // Update pagination
          updatePagination(page, newTotalPages);
          
          return true;
        } else {
          throw new Error('Invalid API response');
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
        const tagsListElement = document.getElementById('tags-list');
        if (tagsListElement) {
          tagsListElement.innerHTML = '<p class="text-center w-full py-4 text-red-600">Error loading tags. Please try again.</p>';
        }
        return false;
      }
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', async () => {
      // Get URL parameters
      const { currentPage, searchQuery } = getUrlParams();
      
      // Update UI
      updatePageUI(currentPage, searchQuery, totalPages, totalTags);
      
      // Update pagination controls
      updatePagination(currentPage, totalPages);
      
      // If not the first page, get the corresponding page data
      if (currentPage > 1) {
        await fetchTags(currentPage, searchQuery);
      }
    });
  })();<\/script> `], ["  ", '<section class="py-12 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h1 class="text-4xl md:text-5xl font-bold mb-4">Browse Tags</h1> <p class="text-xl max-w-3xl mx-auto">Find relevant content through tags to help you make informed decisions.</p> </div> </section>  <section class="py-8 bg-gray-50 border-b border-gray-200"> <div class="container mx-auto px-4 max-w-5xl"> <form class="max-w-2xl mx-auto" action="/tags" method="get" id="search-form"> <div class="relative"> <input type="text" name="search"', ' placeholder="Search tags..." class="w-full pl-4 pr-14 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"> <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-purple-600 hover:bg-purple-700 text-white p-2 rounded-md transition-colors"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </button> </div> </form> </div> </section>  <section class="py-12 bg-white"> <div class="container mx-auto px-4 max-w-5xl"> ', ' </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h2 class="text-3xl md:text-4xl font-bold mb-6">Looking for Something Specific?</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">Browse our complete article collection to discover more content.</p> <div class="flex flex-wrap justify-center gap-4"> <a href="/articles" class="bg-white text-purple-700 hover:bg-gray-100 font-medium py-3 px-6 rounded-lg transition-colors">\nBrowse All Articles\n</a> <a href="/category" class="bg-purple-600 text-white hover:bg-purple-700 font-medium py-3 px-6 rounded-lg transition-colors border border-purple-500">\nBrowse Categories\n</a> </div> </div> </section>  <script>(function(){', `
    // Configuration information
    const API_BASE_URL = 'http://127.0.0.1:9091';
    const API_VERSION = 'v1';
    
    // Current page information
    let currentPage = 1;
    let searchQuery = '';
    
    // Function to get URL parameters
    function getUrlParams() {
      const urlParams = new URLSearchParams(window.location.search);
      const pageParam = urlParams.get('page');
      const searchParam = urlParams.get('search') || '';
      
      currentPage = pageParam ? parseInt(pageParam) : 1;
      if (isNaN(currentPage) || currentPage < 1) currentPage = 1;
      
      searchQuery = searchParam;
      
      console.log('Client-side detected page:', currentPage, 'search:', searchQuery);
      return { currentPage, searchQuery };
    }
    
    // Update UI function
    function updatePageUI(page, search, totalPages, totalTags) {
      // Update page title
      const pageTitle = document.getElementById('page-title');
      if (pageTitle) {
        pageTitle.textContent = search 
          ? \\\`Search Results for "\\\${search}"\\\` 
          : page > 1 
            ? \\\`All Tags - Page \\\${page}\\\` 
            : "All Tags";
      }
      
      // Hide/show featured tags
      const featuredContainer = document.getElementById('featured-tags-container');
      if (featuredContainer) {
        featuredContainer.style.display = (page > 1 || search) ? 'none' : 'block';
      }
      
      // Update page number information
      const paginationInfo = document.getElementById('pagination-info');
      const paginationTotalPages = document.getElementById('pagination-total-pages');
      if (paginationInfo) {
        const pageNumSpan = paginationInfo.querySelector('span.font-medium');
        if (pageNumSpan) pageNumSpan.textContent = page.toString();
        if (paginationTotalPages) paginationTotalPages.textContent = totalPages.toString();
      }
      
      // Show/hide pagination container
      const paginationContainer = document.getElementById('pagination-container');
      if (paginationContainer) {
        paginationContainer.style.display = totalPages > 1 ? 'block' : 'none';
      }
    }
    
    // Update pagination controls - Enhanced version
    function updatePagination(currentPage, totalPages) {
      const paginationElement = document.getElementById('pagination-controls');
      if (!paginationElement || totalPages <= 1) return;
      
      // Clear existing pagination
      paginationElement.innerHTML = '';
      
      // Function to generate pagination URL
      function getPaginationUrl(page) {
        const params = new URLSearchParams();
        if (searchQuery) params.set('search', searchQuery);
        if (page > 1) params.set('page', page.toString());
        return \\\`/tags\\\${params.toString() ? \\\`?\\\${params.toString()}\\\` : ''}\\\`;
      }
      
      // Calculate pagination range
      function getPaginationRange(current, total) {
        let start = Math.max(1, current - 1);
        let end = Math.min(total, start + 2);
        
        if (end - start < 2 && total > 2) {
          start = Math.max(1, end - 2);
        }
        
        // Ensure at least the first three pages are displayed
        if (current <= 3) {
          start = 1;
          end = Math.min(total, 3);
        }
        
        // Ensure at least the last few pages are displayed
        if (current >= total - 2) {
          end = total;
          start = Math.max(1, total - 2);
        }
        
        return Array.from({ length: end - start + 1 }, (_, i) => start + i);
      }

      // Create pagination button
      function createPaginationButton(text, href, isActive = false, isDisabled = false, ariaLabel = '') {
        const button = document.createElement(isDisabled ? 'span' : 'a');
        if (!isDisabled) button.href = href;
        
        // Basic style
        let className = 'inline-flex items-center justify-center min-w-[36px] h-9 px-2 mx-0.5 text-sm font-medium transition-colors ';
        
        // Status style
        if (isActive) {
          className += 'text-white bg-purple-600 rounded-md shadow-sm';
        } else if (isDisabled) {
          className += 'text-gray-400 cursor-not-allowed bg-gray-50 rounded-md';
        } else {
          className += 'text-gray-700 hover:bg-purple-50 hover:text-purple-600 rounded-md';
        }
        
        button.className = className;
        
        if (ariaLabel) button.setAttribute('aria-label', ariaLabel);
        if (isActive) button.setAttribute('aria-current', 'page');
        
        button.innerHTML = text;
        return button;
      }
      
      // First page button
      if (totalPages > 3) {
        const firstButton = createPaginationButton(
          '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" /><path fill-rule="evenodd" d="M9.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" /></svg>',
          getPaginationUrl(1),
          false,
          currentPage === 1,
          'Go to first page'
        );
        paginationElement.appendChild(firstButton);
      }
      
      // Previous page button
      const prevButton = createPaginationButton(
        '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>',
        getPaginationUrl(currentPage - 1),
        false,
        currentPage === 1,
        'Previous page'
      );
      paginationElement.appendChild(prevButton);
      
      // Page number buttons
      const paginationPages = getPaginationRange(currentPage, totalPages);
      
      // If the range doesn't start from 1, add ellipsis
      if (paginationPages[0] > 1) {
        if (paginationPages[0] > 2) {
          // If the distance is far, add "1" button and ellipsis
          const firstPageButton = createPaginationButton('1', getPaginationUrl(1));
          paginationElement.appendChild(firstPageButton);
          
          const ellipsis = createPaginationButton('...', '#', false, true);
          paginationElement.appendChild(ellipsis);
        } else {
          // If only difference is 1, directly add "1" button
          const firstPageButton = createPaginationButton('1', getPaginationUrl(1));
          paginationElement.appendChild(firstPageButton);
        }
      }
      
      // Add page number buttons
      paginationPages.forEach(pageNum => {
        const pageButton = createPaginationButton(
          pageNum.toString(),
          getPaginationUrl(pageNum),
          pageNum === currentPage,
          false,
          \\\`Page \\\${pageNum}\\\`
        );
        paginationElement.appendChild(pageButton);
      });
      
      // If the range doesn't reach the last page, add ellipsis
      if (paginationPages[paginationPages.length - 1] < totalPages) {
        if (paginationPages[paginationPages.length - 1] < totalPages - 1) {
          // If the distance is far, add ellipsis and last page button
          const ellipsis = createPaginationButton('...', '#', false, true);
          paginationElement.appendChild(ellipsis);
          
          const lastPageButton = createPaginationButton(totalPages.toString(), getPaginationUrl(totalPages));
          paginationElement.appendChild(lastPageButton);
        } else {
          // If only difference is 1, directly add last page button
          const lastPageButton = createPaginationButton(totalPages.toString(), getPaginationUrl(totalPages));
          paginationElement.appendChild(lastPageButton);
        }
      }
      
      // Next page button
      const nextButton = createPaginationButton(
        '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>',
        getPaginationUrl(currentPage + 1),
        false,
        currentPage === totalPages,
        'Next page'
      );
      paginationElement.appendChild(nextButton);
      
      // Last page button
      if (totalPages > 3) {
        const lastButton = createPaginationButton(
          '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" /><path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>',
          getPaginationUrl(totalPages),
          false,
          currentPage === totalPages,
          'Go to last page'
        );
        paginationElement.appendChild(lastButton);
      }
    }
    
    // Get tag data
    async function fetchTags(page, search) {
      try {
        document.getElementById('tags-list').innerHTML = '<p class="text-center w-full py-8">Loading page data...</p>';
        
        // Build API URL
        const queryParams = new URLSearchParams();
        queryParams.set('page', page.toString());
        queryParams.set('page_size', pageSize.toString());
        if (search) queryParams.set('search', search);
        
        const apiUrl = \\\`\\\${API_BASE_URL}/api/\\\${API_VERSION}/tag?\\\${queryParams.toString()}\\\`;
        console.log('Fetching tags from:', apiUrl);
        
        // Get data
        const response = await fetch(apiUrl);
        const data = await response.json();
        
        if (data && data.code === 200 && data.data && data.data.tag_list) {
          const tags = data.data.tag_list;
          console.log(\\\`Fetched \\\${tags.length} tags for page \\\${page}\\\`);
          
          // No longer update removed count display
          
          // Update total pages
          const newTotalPages = Math.ceil(data.data.total / pageSize);
          
          // Render tags
          const tagsListElement = document.getElementById('tags-list');
          if (tagsListElement) {
            if (tags.length === 0) {
              tagsListElement.innerHTML = '<p class="text-center w-full py-4 text-gray-600">No tags found for this page.</p>';
            } else {
              tagsListElement.innerHTML = '';
              
              // Create tag elements
              tags.forEach(tag => {
                const tagElement = document.createElement('a');
                tagElement.href = \\\`/articles?tag=\\\${tag.slug}\\\`;
                tagElement.className = "text-sm px-4 py-2 bg-white border border-gray-200 hover:border-purple-300 text-gray-700 hover:text-purple-700 rounded-full font-medium transition-colors whitespace-nowrap";
                // Add # prefix to tag name
                tagElement.textContent = \\\`# \\\${tag.name}\\\`;
                tagsListElement.appendChild(tagElement);
              });
            }
          }
          
          // Update pagination
          updatePagination(page, newTotalPages);
          
          return true;
        } else {
          throw new Error('Invalid API response');
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
        const tagsListElement = document.getElementById('tags-list');
        if (tagsListElement) {
          tagsListElement.innerHTML = '<p class="text-center w-full py-4 text-red-600">Error loading tags. Please try again.</p>';
        }
        return false;
      }
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', async () => {
      // Get URL parameters
      const { currentPage, searchQuery } = getUrlParams();
      
      // Update UI
      updatePageUI(currentPage, searchQuery, totalPages, totalTags);
      
      // Update pagination controls
      updatePagination(currentPage, totalPages);
      
      // If not the first page, get the corresponding page data
      if (currentPage > 1) {
        await fetchTags(currentPage, searchQuery);
      }
    });
  })();<\/script> `])), maybeRenderHead(), addAttribute(search, "value"), errorMessage ? renderTemplate`<div class="card p-8 text-center"> <p class="text-lg text-purple-600 mb-4">${errorMessage}</p> <button onclick="window.location.reload()" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
Try Again
</button> </div>` : renderTemplate`<div> <!-- Featured Tags Section --> <div id="featured-tags-container" class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">
Featured Tags
</h2> <div class="flex flex-wrap gap-3"> ${renderComponent($$result2, "TagList", $$TagList, { "tags": featuredTags })} </div> </div> <!-- Main Tags Area --> <div class="mb-8"> <h2 class="text-2xl font-bold" id="page-title">${pageTitle}</h2> </div> <div id="tags-content" class="mb-12"> ${allTags.length === 0 ? renderTemplate`<div class="card p-8 text-center bg-gray-50 rounded-lg"> <p class="text-lg text-gray-600 mb-4">No tags found matching your search.</p> <a href="/tags" class="text-purple-600 hover:text-purple-800 font-medium">View all tags</a> </div>` : renderTemplate`<div class="flex flex-wrap gap-4" id="tags-list"> ${renderComponent($$result2, "TagList", $$TagList, { "tags": allTags })} </div>`} </div> <!-- Pagination Controls - Enhanced Style --> <div class="mt-12" id="pagination-container"> <div class="flex flex-col sm:flex-row justify-between items-center gap-4"> <!-- Left Side Page Information --> <div class="text-sm text-gray-500" id="pagination-info">
Page <span class="font-medium text-purple-600">1</span> of <span id="pagination-total-pages">${totalPages}</span> </div> <!-- Right Side Pagination Navigation --> <nav class="inline-flex items-center" aria-label="Pagination" id="pagination-controls"> <!-- Pagination controls will be generated by JavaScript --> </nav> </div> </div> </div>`, defineScriptVars({ totalTags, totalPages, pageSize })) })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/tags/index.astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/tags/index.astro";
const $$url = "/tags";

const index = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { index$1 as a, index as b, index$2 as i };
