/* empty css                           */import { e as createComponent, r as renderTemplate, j as renderComponent, m as maybeRenderHead, f as addAttribute, u as unescapeHTML } from '../astro_0d22710d.mjs';
import { $ as $$MainLayout, s as siteConfig } from './_slug__fe545221.mjs';

const $$SampleWithBrandDeal = createComponent(($$result, $$props, $$slots) => {
  const article = {
    id: 1,
    title: "Sony WH-1000XM5 Review: The Best Noise-Cancelling Headphones",
    content: `
    <p class="mb-4">Sony has been leading the noise-cancelling headphone market for years, and the WH-1000XM5 continues this tradition of excellence. These premium wireless headphones offer improved noise cancellation, exceptional sound quality, and impressive battery life.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Design and Build Quality</h2>
    
    <p class="mb-4">The WH-1000XM5 features a sleeker, more refined design compared to its predecessors. The headband is thinner with a new synthetic leather wrap that feels premium to the touch. The ear cups are slightly larger and more oval-shaped, providing better comfort for extended listening sessions.</p>
    
    <p class="mb-4">Despite using more plastic in the construction, the headphones feel sturdy and well-built. They're also lighter than the previous model, weighing just 250g, which makes them more comfortable to wear for long periods.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Noise Cancellation</h2>
    
    <p class="mb-4">Sony has taken noise cancellation to a new level with the WH-1000XM5. With eight microphones (up from four in the previous model) and two processors working together, these headphones effectively block out everything from airplane engine noise to office chatter.</p>
    
    <p class="mb-4">The Auto NC Optimizer function automatically adjusts the noise cancellation based on your environment, ensuring optimal performance regardless of where you're using them. Additionally, the Adaptive Sound Control feature can detect your activities and locations to adjust ambient sound settings accordingly.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Sound Quality</h2>
    
    <p class="mb-4">Sony has equipped the WH-1000XM5 with new 30mm drivers that deliver incredibly detailed and balanced sound. The bass is punchy without being overwhelming, the mids are clear and present, and the highs are crisp without becoming harsh.</p>
    
    <p class="mb-4">These headphones support Sony's LDAC codec, which allows for high-resolution audio streaming over Bluetooth. While they don't support aptX, the overall sound quality is exceptional regardless of the source material.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Battery Life and Connectivity</h2>
    
    <p class="mb-4">Battery life remains impressive at around 30 hours with noise cancellation enabled, and a quick 3-minute charge provides about 3 hours of playback. The headphones use Bluetooth 5.2, which provides a stable connection and supports multipoint connectivity for pairing with two devices simultaneously.</p>
    
    <p class="mb-4">The touch controls on the right ear cup are intuitive and responsive, allowing for easy control of playback, volume, and calls. The headphones also pause automatically when removed and resume when put back on.</p>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Pros and Cons</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div>
        <h3 class="text-lg font-bold mb-2">What We Like</h3>
        <ul class="list-disc pl-5 space-y-1">
          <li>Best-in-class noise cancellation</li>
          <li>Detailed, balanced sound profile</li>
          <li>Comfortable for long listening sessions</li>
          <li>Excellent call quality with 8 microphones</li>
          <li>Strong battery life (30 hours)</li>
          <li>Multipoint Bluetooth connection</li>
        </ul>
      </div>
      <div>
        <h3 class="text-lg font-bold mb-2">What We Don't Like</h3>
        <ul class="list-disc pl-5 space-y-1">
          <li>Premium price point</li>
          <li>No aptX codec support</li>
          <li>No IP rating for water/dust resistance</li>
          <li>Not as compact as previous model</li>
          <li>Can't fold flat for storage</li>
        </ul>
      </div>
    </div>
    
    <h2 class="text-2xl font-bold mt-8 mb-4">Verdict</h2>
    
    <p class="mb-4">The Sony WH-1000XM5 represents the pinnacle of wireless noise-cancelling headphones in 2023. While they come with a premium price tag of $399, the exceptional noise cancellation, superb sound quality, and long battery life make them worth the investment for those who value audio performance and peace and quiet.</p>
    
    <p class="mb-4">If you're looking for the absolute best noise-cancelling headphones on the market and don't mind paying a premium, the Sony WH-1000XM5 should be at the top of your list.</p>
  `,
    excerpt: "Sony's WH-1000XM5 takes noise-cancelling headphones to a new level with improved sound quality, better ANC performance, and premium comfort.",
    image: "/images/sony-headphones.jpg",
    category: "Audio",
    brand: "Sony",
    brandInfo: {
      name: "Sony",
      logo: "/images/brands/sony-logo.png",
      description: "Sony Corporation is a Japanese multinational conglomerate corporation headquartered in Tokyo, Japan. As a major technology company, it's one of the leading manufacturers of electronic products for the consumer and professional markets.",
      website: "https://www.sony.com"
    },
    relatedDeals: [
      {
        id: 1,
        title: "Sony WH-1000XM5 Wireless Headphones",
        discount: "15% OFF",
        price: "$339.00",
        originalPrice: "$399.99",
        image: "/images/deals/sony-headphones-deal.jpg",
        store: "Amazon",
        expiry: "2023-12-31",
        url: "https://example.com/deal/sony-wh1000xm5"
      },
      {
        id: 2,
        title: "Sony WH-1000XM5 + Sony WF-1000XM4 Bundle",
        discount: "20% OFF",
        price: "$499.99",
        originalPrice: "$629.98",
        image: "/images/deals/sony-bundle-deal.jpg",
        store: "Best Buy",
        expiry: "2023-12-15",
        url: "https://example.com/deal/sony-bundle"
      }
    ],
    publishedDate: "November 15, 2023",
    author: "John Smith",
    authorImage: "/images/authors/john-smith.jpg",
    authorBio: "John is our senior audio equipment reviewer with over 10 years of experience in the consumer electronics industry.",
    slug: "sony-wh-1000xm5-review",
    tags: ["Sony", "Headphones", "Wireless", "Noise Cancelling", "Premium Audio"]
  };
  const relatedArticles = [
    {
      id: 3,
      title: "Bose QuietComfort Ultra Headphones Review",
      excerpt: "How do Bose's new flagship noise-cancelling headphones compare to the competition? We put them to the test.",
      image: "/images/bose-headphones.jpg",
      category: "Audio",
      publishedDate: "November 10, 2023",
      author: "John Smith",
      slug: "bose-quietcomfort-ultra-review"
    },
    {
      id: 4,
      title: "Sony WF-1000XM5 Earbuds Review",
      excerpt: "Sony's latest noise-cancelling earbuds offer incredible performance in a compact package.",
      image: "/images/sony-earbuds.jpg",
      category: "Audio",
      publishedDate: "October 25, 2023",
      author: "Sarah Wilson",
      slug: "sony-wf-1000xm5-earbuds-review"
    },
    {
      id: 5,
      title: "Best Noise-Cancelling Headphones of 2023",
      excerpt: "Our comprehensive guide to the top noise-cancelling headphones available this year.",
      image: "/images/best-nc-headphones.jpg",
      category: "Audio",
      publishedDate: "October 5, 2023",
      author: "Michael Brown",
      slug: "best-noise-cancelling-headphones-2023"
    }
  ];
  const hasSidebar = article.brandInfo || article.relatedDeals && article.relatedDeals.length > 0;
  const { containerWidth } = siteConfig;
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": `${article.title} | SmartReviews`, "description": article.excerpt, "image": article.image }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="bg-gray-50 py-3"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <nav class="flex text-sm"> <a href="/" class="text-gray-500 hover:text-purple-600">Home</a> <span class="mx-2 text-gray-400">/</span> <a${addAttribute(`/articles?category=${article.category}`, "href")} class="text-gray-500 hover:text-purple-600">${article.category}</a> <span class="mx-2 text-gray-400">/</span> <span class="text-gray-700">${article.title}</span> </nav> </div> </div>  <header class="py-8 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="max-w-full mx-auto"> <span class="inline-block bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full mb-4"> ${article.category} </span> <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">${article.title}</h1> <p class="text-xl text-gray-600 mb-6">${article.excerpt}</p> <div class="flex items-center space-x-4 mb-6"> <img${addAttribute(article.authorImage, "src")}${addAttribute(article.author, "alt")} class="w-12 h-12 rounded-full object-cover"> <div> <div class="font-medium">${article.author}</div> <div class="text-sm text-gray-500">${article.publishedDate}</div> </div> </div> <div class="flex flex-wrap gap-2 mb-8"> ${article.tags.map((tag) => renderTemplate`<a${addAttribute(`/articles?tag=${tag.toLowerCase().replace(" ", "-")}`, "href")} class="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm px-3 py-1 rounded-full transition-colors"> ${tag} </a>`)} </div> </div> </div> </header>  <div${addAttribute(`container mx-auto px-4 ${containerWidth} mb-10`, "class")}> <div class="max-w-full mx-auto"> <div class="relative pb-[40%] overflow-hidden rounded-xl"> <img${addAttribute(article.image, "src")}${addAttribute(article.title, "alt")} class="absolute inset-0 w-full h-full object-cover"> </div> </div> </div>  <div${addAttribute(`container mx-auto px-4 ${containerWidth} mb-16`, "class")}> <div${addAttribute(`grid ${hasSidebar ? "grid-cols-1 lg:grid-cols-3 gap-8" : "grid-cols-1"}`, "class")}> <!-- 左侧主内容 --> <div${addAttribute(`${hasSidebar ? "lg:col-span-2" : "max-w-4xl mx-auto w-full"}`, "class")}> <article class="prose prose-lg max-w-none"> <div>${unescapeHTML(article.content)}</div> </article> <!-- 作者信息 --> <div class="mt-12 p-6 bg-gray-50 rounded-xl"> <div class="flex items-start space-x-4"> <img${addAttribute(article.authorImage, "src")}${addAttribute(article.author, "alt")} class="w-16 h-16 rounded-full object-cover"> <div> <h3 class="font-bold text-lg">About ${article.author}</h3> <p class="text-gray-600 mt-1">${article.authorBio}</p> </div> </div> </div> <!-- 分享按钮 --> <div class="mt-8 flex items-center justify-between"> <div class="text-gray-700 font-medium">Share this article:</div> <div class="flex space-x-4"> <a href="#" class="text-gray-500 hover:text-blue-600"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path> </svg> </a> <a href="#" class="text-gray-500 hover:text-blue-400"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path> </svg> </a> <a href="#" class="text-gray-500 hover:text-green-600"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd"></path> </svg> </a> </div> </div> </div>  ${hasSidebar && renderTemplate`<div class="lg:col-span-1">  ${article.brandInfo && renderTemplate`<div class="mb-8 bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100"> <div class="p-6"> <h3 class="text-xl font-bold mb-4">About ${article.brandInfo.name}</h3> <div class="flex justify-center mb-4"> <img${addAttribute(article.brandInfo.logo, "src")}${addAttribute(article.brandInfo.name, "alt")} class="h-16 object-contain"> </div> <p class="text-gray-600 text-sm mb-4">${article.brandInfo.description}</p> <a${addAttribute(article.brandInfo.website, "href")} target="_blank" rel="noopener noreferrer" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-3 rounded-lg font-medium transition-colors">
Shop Now
</a> </div> </div>`}  ${article.relatedDeals && article.relatedDeals.length > 0 && renderTemplate`<div class="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100"> <div class="p-6"> <h3 class="text-xl font-bold mb-4">Related Deals</h3> <div class="space-y-4"> ${article.relatedDeals.map((deal) => renderTemplate`<div class="border border-gray-100 rounded-lg overflow-hidden"> <div class="relative"> <img${addAttribute(deal.image, "src")}${addAttribute(deal.title, "alt")} class="w-full h-40 object-cover"> <span class="absolute top-2 right-2 bg-purple-600 text-white font-bold px-3 py-1 rounded-full text-sm"> ${deal.discount} </span> </div> <div class="p-4"> <h4 class="font-bold text-gray-900 mb-2">${deal.title}</h4> <div class="flex items-center mb-3"> <span class="text-xl font-bold text-purple-700">${deal.price}</span> <span class="ml-2 text-sm text-gray-500 line-through">${deal.originalPrice}</span> </div> <div class="flex justify-between items-center"> <span class="text-xs text-gray-500">From ${deal.store}</span> <span class="text-xs text-gray-500">Expires: ${deal.expiry}</span> </div> <a${addAttribute(deal.url, "href")} target="_blank" rel="noopener noreferrer" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 rounded-lg font-medium transition-colors mt-4">
Get This Deal
</a> </div> </div>`)} </div> </div> </div>`} </div>`} </div> </div>  <section class="py-12 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <h2 class="text-3xl font-bold mb-8 text-center">Related Articles</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> ${relatedArticles.map((article2) => renderTemplate`<article class="card h-full flex flex-col"> <div class="relative pb-[60%] overflow-hidden"> <img${addAttribute(article2.image, "src")}${addAttribute(article2.title, "alt")} class="absolute inset-0 w-full h-full object-cover transition-transform hover:scale-105"> <span class="absolute top-4 left-4 bg-purple-600 text-white text-sm font-medium px-3 py-1 rounded-full"> ${article2.category} </span> </div> <div class="p-6 flex-grow flex flex-col"> <h3 class="text-xl font-bold mb-3"> <a${addAttribute(`/article/${article2.slug}`, "href")} class="text-gray-900 hover:text-purple-600"> ${article2.title} </a> </h3> <p class="text-gray-600 mb-4 flex-grow">${article2.excerpt}</p> <div class="flex justify-between items-center text-sm text-gray-500"> <span>${article2.publishedDate}</span> <span>By ${article2.author}</span> </div> </div> </article>`)} </div> </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth} text-center`, "class")}> <h2 class="text-3xl md:text-4xl font-bold mb-6">Stay Updated with the Latest Reviews</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">Subscribe to our newsletter and never miss a new product review or buying guide.</p> <form class="max-w-xl mx-auto flex flex-col sm:flex-row gap-4"> <input type="email" placeholder="Your email address" class="flex-grow px-4 py-3 rounded-md focus:outline-none focus:ring-2 focus:ring-white text-gray-900" required> <button type="submit" class="bg-white text-purple-700 hover:bg-gray-100 px-6 py-3 rounded-md font-medium whitespace-nowrap">
Subscribe
</button> </form> </div> </section> ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/article/sample-with-brand-deal.astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/article/sample-with-brand-deal.astro";
const $$url = "/article/sample-with-brand-deal";

export { $$SampleWithBrandDeal as default, $$file as file, $$url as url };
