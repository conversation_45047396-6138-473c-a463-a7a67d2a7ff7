/* empty css                           */import { e as createComponent, r as renderTemplate, m as maybeRenderHead, f as addAttribute, g as create<PERSON>tro, h as renderHead, i as renderSlot, j as renderComponent, u as unescapeHTML } from '../astro_0d22710d.mjs';
import 'clsx';

const siteConfig = {
  // Site metadata
  name: "SmartReviews",
  description: "Your trusted source for honest product reviews and recommendations.",
  // Layout
  containerWidth: "max-w-6xl",
  // Used across all pages for consistent width
  // Theme colors
  theme: {
    primary: "purple",
    // Primary color name (using Tailwind scale)
    colorMap: {
      // Map for color intensity values - Tailwind color scale
      50: "purple-50",
      100: "purple-100",
      200: "purple-200",
      300: "purple-300",
      400: "purple-400",
      500: "purple-500",
      600: "purple-600",
      700: "purple-700",
      800: "purple-800",
      900: "purple-900"
    }
  },
  // Navigation
  navigation: [
    {
      title: "Home",
      href: "/"
    },
    {
      title: "Categories",
      href: "/category"
    },
    {
      title: "Brands",
      href: "/brands"
    },
    {
      title: "Deals",
      href: "/deals"
    },
    {
      title: "About",
      href: "/about"
    }
  ],
  // Footer links
  footerLinks: [
    {
      title: "Home",
      href: "/"
    },
    {
      title: "Categories",
      href: "/category"
    },
    {
      title: "Brands",
      href: "/brands"
    },
    {
      title: "About",
      href: "/about"
    },
    {
      title: "Privacy Policy",
      href: "/privacy-policy"
    }
  ],
  // Social media
  socialMedia: {
    facebook: "https://facebook.com/smartreviews",
    twitter: "https://twitter.com/smartreviews",
    instagram: "https://instagram.com/smartreviews"
  }
};

const $$Footer = createComponent(($$result, $$props, $$slots) => {
  const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  return renderTemplate`${maybeRenderHead()}<footer class="bg-gray-900 text-white py-8 sm:py-12 relative z-10"> <div${addAttribute(`container mx-auto px-3 sm:px-4 ${siteConfig.containerWidth}`, "class")}> <!-- 主要内容分为两行：第一行为介绍和上半部分，第二行为下半部分 --> <div class="grid grid-cols-1 md:grid-cols-4 gap-8"> <!-- About Section --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">${siteConfig.name}</h3> <p class="text-gray-300 mb-4">${siteConfig.description}</p> <p class="text-gray-300 text-sm">
Smart, honest product reviews and comparisons to help you make informed purchasing decisions.
</p> </div> <!-- First Row - First Column (Quick Links) --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Quick Links</h3> <ul class="space-y-2"> ${siteConfig.footerLinks.map((link) => renderTemplate`<li><a${addAttribute(link.href, "href")} class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">${link.title}</a></li>`)} </ul> </div> <!-- First Row - Second Column (Featured Brands) --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Featured Brands</h3> <ul class="space-y-2"> <li><a href="/articles?brand=Apple" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Apple</a></li> <li><a href="/articles?brand=Samsung" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Samsung</a></li> <li><a href="/articles?brand=Sony" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Sony</a></li> <li><a href="/brands" class="text-purple-400 hover:text-purple-300 transition-all duration-300 hover:translate-x-1 inline-block text-sm mt-2">View All Brands →</a></li> </ul> </div> <!-- First Row - Third Column (Subscribe) --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Subscribe</h3> <p class="text-gray-300 mb-4">Get the latest reviews and deals delivered straight to your inbox.</p> <form class="flex"> <input type="email" placeholder="Your email" class="flex-grow px-4 py-2 rounded-l-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900" required> <button type="submit" class="bg-gradient-to-r from-purple-500 to-purple-700 hover:from-purple-600 hover:to-purple-800 text-white px-4 py-2 rounded-r-md font-medium transition-all duration-300">
Subscribe
</button> </form> </div> </div> <!-- 第二行的内容 --> <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mt-8 pt-8 border-t border-gray-700"> <!-- 空白占位，保持与上面的布局对齐 --> <div class="md:col-span-1"> <!-- 留空 --> </div> <!-- Second Row - First Column (Popular Categories) --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Popular Categories</h3> <ul class="space-y-2"> <li><a href="/articles?category=Smartphones" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Smartphones</a></li> <li><a href="/articles?category=Laptops" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Laptops</a></li> <li><a href="/articles?category=Audio" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Audio</a></li> <li><a href="/category" class="text-purple-400 hover:text-purple-300 transition-all duration-300 hover:translate-x-1 inline-block text-sm mt-2">View All Categories →</a></li> </ul> </div> <!-- Second Row - Second Column (Today's Deals) --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Today's Deals</h3> <ul class="space-y-2"> <li> <a href="/deals" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-flex items-center"> <span class="bg-purple-600 text-white text-xs px-2 py-0.5 rounded-full mr-2 flex-shrink-0">40% OFF</span> <span>Wireless Headphones</span> </a> </li> <li> <a href="/deals" class="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1 inline-flex items-center"> <span class="bg-purple-600 text-white text-xs px-2 py-0.5 rounded-full mr-2 flex-shrink-0">NEW</span> <span>Smart Home Bundles</span> </a> </li> <li><a href="/deals" class="text-purple-400 hover:text-purple-300 transition-all duration-300 hover:translate-x-1 inline-block text-sm mt-2">View All Deals →</a></li> </ul> </div> <!-- Second Row - Third Column (Latest Reviews) --> <div class="md:col-span-1"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Latest Reviews</h3> <div class="space-y-3"> <a href="/article/iphone-15-pro-max-review" class="group flex items-center gap-3"> <div class="w-12 h-12 rounded-md overflow-hidden flex-shrink-0"> <img src="/images/iphone-15.jpg" alt="iPhone 15" class="w-full h-full object-cover group-hover:scale-110 transition-transform"> </div> <div> <p class="text-gray-300 group-hover:text-white transition-colors text-sm">iPhone 15 Pro Max Review</p> <p class="text-gray-500 text-xs">October 12, 2023</p> </div> </a> <a href="/articles" class="text-xs text-purple-400 hover:text-purple-300 inline-block mt-1">View All Articles →</a> </div> </div> </div> <!-- Tags Section --> <div class="mt-8 pt-8 border-t border-gray-800"> <h3 class="text-xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-200">Popular Tags</h3> <div class="flex flex-wrap gap-2"> <a href="/articles?tag=wireless" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#Wireless</a> <a href="/articles?tag=budget" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#Budget</a> <a href="/articles?tag=premium" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#Premium</a> <a href="/articles?tag=gaming" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#Gaming</a> <a href="/articles?tag=portable" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#Portable</a> <a href="/articles?tag=bluetooth" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#Bluetooth</a> <a href="/articles?tag=usb-c" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#USB-C</a> <a href="/articles?tag=5g" class="text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white px-2 py-1 rounded-full transition-colors">#5G</a> <a href="/tags" class="text-xs text-purple-400 hover:text-purple-300 ml-1">View All Tags →</a> </div> </div> <!-- Copyright and Legal --> <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center text-gray-400 text-sm"> <p>&copy; ${currentYear} ${siteConfig.name}. All rights reserved.</p> <div class="mt-4 md:mt-0 flex space-x-4"> <a href="/privacy-policy" class="hover:text-white transition-colors">Privacy Policy</a> <a href="/terms-of-service" class="hover:text-white transition-colors">Terms of Service</a> <a href="/contact" class="hover:text-white transition-colors">Contact Us</a> </div> </div> </div> </footer>`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/components/Footer.astro", void 0);

const $$Astro$2 = createAstro("https://smartreviews.com");
const $$MainLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$MainLayout;
  const { title, description, image = "/images/default-og.png" } = Astro2.props;
  const { containerWidth } = siteConfig;
  const canonicalURL = new URL(Astro2.url.pathname, Astro2.site);
  return renderTemplate`<html lang="en"> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>${title}</title><meta name="description"${addAttribute(description, "content")}><link rel="canonical"${addAttribute(canonicalURL, "href")}><!-- Open Graph / Facebook --><meta property="og:type" content="website"><meta property="og:url"${addAttribute(canonicalURL, "content")}><meta property="og:title"${addAttribute(title, "content")}><meta property="og:description"${addAttribute(description, "content")}><meta property="og:image"${addAttribute(new URL(image, Astro2.site), "content")}><!-- Twitter --><meta property="twitter:card" content="summary_large_image"><meta property="twitter:url"${addAttribute(canonicalURL, "content")}><meta property="twitter:title"${addAttribute(title, "content")}><meta property="twitter:description"${addAttribute(description, "content")}><meta property="twitter:image"${addAttribute(new URL(image, Astro2.site), "content")}><!-- Fonts --><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:wght@400;700&display=swap" rel="stylesheet"><!-- Favicon --><link rel="icon" type="image/svg+xml" href="/favicon.svg">${renderHead()}</head> <body class="flex flex-col min-h-screen bg-gray-50"> <header class="bg-white sticky top-0 z-50 shadow-md transition-all duration-300 backdrop-blur-sm bg-white/95"> <div${addAttribute(`container mx-auto px-3 sm:px-4 ${containerWidth} py-3 sm:py-4 flex justify-between items-center`, "class")}> <a href="/" class="text-2xl font-bold text-purple-700 flex items-center transition-transform hover:scale-105"> <span class="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-purple-800">${siteConfig.name}</span> </a> <nav class="hidden md:flex space-x-6"> ${siteConfig.navigation.map((item) => renderTemplate`<a${addAttribute(item.href, "href")} class="text-gray-700 hover:text-purple-600 font-medium relative group transition-colors duration-300"> ${item.title} <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-purple-600 transition-all duration-300 group-hover:w-full"></span> </a>`)} </nav> <div class="flex items-center space-x-4"> <div class="relative hidden md:block"> <input type="text" placeholder="Search articles..." class="pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-sm transition-all duration-300 hover:shadow-md"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <!-- Mobile menu button --> <button class="md:hidden text-gray-700 hover:text-purple-600 transition-colors duration-300" id="mobile-menu-button"> <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </div> <!-- Mobile menu --> <div class="md:hidden hidden bg-white absolute w-full shadow-lg z-40" id="mobile-menu"> <div${addAttribute(`container mx-auto px-3 sm:px-4 ${containerWidth} py-3 space-y-3`, "class")}> <div class="relative"> <input type="text" placeholder="Search articles..." class="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> ${siteConfig.navigation.map((item) => renderTemplate`<a${addAttribute(item.href, "href")} class="block text-gray-700 hover:text-purple-600 font-medium py-2 border-b border-gray-100 transition-colors duration-300">${item.title}</a>`)} </div> </div> </header> <main class="flex-grow relative z-0"> ${renderSlot($$result, $$slots["default"])} </main> ${renderComponent($$result, "Footer", $$Footer, {})}  </body> </html>`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/layouts/MainLayout.astro", void 0);

const $$Astro$1 = createAstro("https://smartreviews.com");
async function getStaticPaths$1() {
  const allArticles = [
    {
      id: 1,
      title: "Top 10 Wireless Headphones for 2023",
      content: `
        <p class="mb-4">Wireless headphones have become an essential accessory for many people, offering convenience and freedom from tangled cords. In this comprehensive review, we've tested dozens of the latest models to bring you our top picks for 2023.</p>
        
        <h2 class="text-2xl font-bold mt-8 mb-4">What to Look for in Wireless Headphones</h2>
        
        <p class="mb-4">Before diving into our recommendations, here are the key factors we considered in our evaluation:</p>
        
        <ul class="list-disc pl-6 mb-6 space-y-2">
          <li><strong>Sound Quality:</strong> The most important aspect of any headphones is how they sound. We tested each model with various music genres, podcasts, and during calls.</li>
          <li><strong>Battery Life:</strong> We measured how long each pair lasted on a single charge during continuous playback at moderate volume levels.</li>
          <li><strong>Comfort:</strong> Headphones should be comfortable for extended listening sessions. We wore each pair for at least 3 hours straight to assess comfort.</li>
          <li><strong>Noise Cancellation:</strong> For models with ANC (Active Noise Cancellation), we tested how effectively they blocked out different types of ambient noise.</li>
          <li><strong>Connectivity:</strong> We evaluated Bluetooth range, stability, and multipoint connection capabilities.</li>
          <li><strong>Build Quality:</strong> Durability is essential for a product you'll use daily. We assessed materials, construction, and included any manufacturer warranty information.</li>
        </ul>
        
        <h2 class="text-2xl font-bold mt-8 mb-4">Our Top Picks</h2>
        
        <div class="mb-8 pb-8 border-b border-gray-200">
          <h3 class="text-xl font-bold mb-3">1. Sony WH-1000XM5</h3>
          <div class="flex flex-col md:flex-row gap-6 mb-4">
            <div class="md:w-1/3">
              <img src="/images/sony-wh1000xm5.jpg" alt="Sony WH-1000XM5" class="rounded-lg w-full h-auto">
            </div>
            <div class="md:w-2/3">
              <div class="flex items-center mb-3">
                <div class="flex items-center">
                  <span class="text-yellow-400">
                    \u2605\u2605\u2605\u2605\u2605
                  </span>
                  <span class="ml-2 text-gray-600">5.0/5.0</span>
                </div>
                <span class="mx-3 text-gray-300">|</span>
                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Editor's Choice</span>
              </div>
              
              <p class="mb-4">The Sony WH-1000XM5 sets a new standard for premium wireless headphones. With industry-leading noise cancellation, exceptional sound quality, and impressive 30-hour battery life, these headphones are worth every penny of their premium price tag.</p>
              
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <h4 class="font-medium text-gray-900">Pros</h4>
                  <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                    <li>Best-in-class noise cancellation</li>
                    <li>Detailed, balanced sound profile</li>
                    <li>Extremely comfortable for long sessions</li>
                    <li>Excellent call quality with 8 microphones</li>
                    <li>Intuitive touch controls</li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900">Cons</h4>
                  <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                    <li>Premium price point</li>
                    <li>No aptX codec support</li>
                    <li>Not as compact as previous model</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <h4 class="text-sm font-medium text-gray-500">Battery Life</h4>
                <p class="text-gray-900">30 hours (ANC on)</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-500">Connectivity</h4>
                <p class="text-gray-900">Bluetooth 5.2</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-500">Weight</h4>
                <p class="text-gray-900">250g</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-500">Price</h4>
                <p class="text-gray-900">$399</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Additional headphone reviews would follow the same pattern -->
        
        <h2 class="text-2xl font-bold mt-8 mb-4">Buying Guide: How to Choose the Right Wireless Headphones</h2>
        
        <p class="mb-4">With so many options available, finding the perfect wireless headphones can be overwhelming. Here's a quick guide to help you make an informed decision:</p>
        
        <h3 class="text-xl font-bold mt-6 mb-3">Headphone Types</h3>
        
        <p class="mb-4">Wireless headphones come in three main styles:</p>
        
        <ul class="list-disc pl-6 mb-6 space-y-2">
          <li><strong>Over-ear:</strong> These completely cover your ears, providing the best sound isolation and typically the best sound quality. They're also usually the most comfortable for long listening sessions but are bulkier to carry.</li>
          <li><strong>On-ear:</strong> These sit on your ears rather than around them. They're more compact than over-ear models but may cause discomfort during extended use.</li>
          <li><strong>In-ear:</strong> Also called earbuds, these fit inside your ear canal. They're the most portable option but may not provide the same sound quality as larger headphones.</li>
        </ul>
        
        <h3 class="text-xl font-bold mt-6 mb-3">Noise Cancellation</h3>
        
        <p class="mb-4">There are three types of noise control to consider:</p>
        
        <ul class="list-disc pl-6 mb-6 space-y-2">
          <li><strong>Active Noise Cancellation (ANC):</strong> Uses microphones and speakers to reduce ambient noise by creating opposing sound waves. Best for consistent, low-frequency sounds like airplane engines or air conditioners.</li>
          <li><strong>Passive Noise Isolation:</strong> Physically blocks outside noise through the headphone design and materials. Effective for higher-frequency sounds.</li>
          <li><strong>Transparency/Ambient Mode:</strong> Allows you to hear your surroundings without removing your headphones\u2014useful for brief conversations or awareness in public spaces.</li>
        </ul>
        
        <h2 class="text-2xl font-bold mt-8 mb-4">Conclusion</h2>
        
        <p class="mb-4">The perfect wireless headphones depend on your specific needs and preferences. If you prioritize noise cancellation and sound quality and don't mind the premium price, the Sony WH-1000XM5 is our top recommendation. For those on a tighter budget, the Anker Soundcore Life Q30 offers exceptional value without compromising too much on features.</p>
        
        <p class="mb-4">Remember that comfort is highly subjective, so whenever possible, try before you buy or purchase from retailers with good return policies.</p>
      `,
      excerpt: "Discover the best wireless headphones that combine superior sound quality, comfort, and battery life for an exceptional listening experience.",
      image: "/images/headphones.jpg",
      category: "Audio",
      brand: "Various",
      publishedDate: "October 15, 2023",
      author: "John Smith",
      authorImage: "/images/authors/john-smith.jpg",
      authorBio: "John is our senior audio equipment reviewer with over 10 years of experience in the consumer electronics industry.",
      slug: "top-10-wireless-headphones-2023",
      tags: ["Wireless", "Audio", "Headphones", "Noise Cancelling", "Bluetooth"]
    },
    {
      id: 2,
      title: "The Ultimate Guide to Choosing a Gaming Laptop",
      content: `<p>Detailed content about gaming laptops would go here...</p>`,
      excerpt: "Everything you need to know about selecting the perfect gaming laptop that meets your performance needs and budget constraints.",
      image: "/images/gaming-laptop.jpg",
      category: "Computers",
      brand: "Various",
      publishedDate: "October 10, 2023",
      author: "Emily Johnson",
      authorImage: "/images/authors/emily-johnson.jpg",
      authorBio: "Emily specializes in gaming hardware and has been covering the tech industry for over 5 years.",
      slug: "ultimate-guide-gaming-laptop",
      tags: ["Gaming", "Laptops", "Computers", "Hardware", "Budget"]
    }
    // 其他文章...
  ];
  return allArticles.map((article) => ({
    params: { slug: article.slug },
    props: { article }
  }));
}
const $$slug$1 = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$slug$1;
  const { article } = Astro2.props;
  const relatedArticles = [
    {
      id: 3,
      title: "Best Noise-Cancelling Earbuds for 2023",
      excerpt: "Looking for peace and quiet in a compact form? These noise-cancelling earbuds deliver impressive sound isolation in a portable package.",
      image: "/images/noise-cancelling-earbuds.jpg",
      category: "Audio",
      publishedDate: "October 12, 2023",
      author: "John Smith",
      slug: "best-noise-cancelling-earbuds-2023"
    },
    {
      id: 4,
      title: "Wired vs. Wireless Headphones: Which Should You Choose?",
      excerpt: "We compare the pros and cons of wired and wireless headphones to help you decide which type is right for your listening needs.",
      image: "/images/wired-vs-wireless.jpg",
      category: "Audio",
      publishedDate: "October 8, 2023",
      author: "Sarah Wilson",
      slug: "wired-vs-wireless-headphones-comparison"
    },
    {
      id: 5,
      title: "How to Extend the Battery Life of Your Wireless Headphones",
      excerpt: "Simple tips and tricks to maximize the battery life of your wireless headphones and earbuds.",
      image: "/images/headphone-battery.jpg",
      category: "Audio",
      publishedDate: "October 3, 2023",
      author: "Michael Brown",
      slug: "extend-wireless-headphone-battery-life"
    }
  ];
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": `${article.title} | SmartReviews`, "description": article.excerpt, "image": article.image }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="bg-gray-50 py-3"> <div class="container mx-auto px-4"> <nav class="flex text-sm"> <a href="/" class="text-gray-500 hover:text-primary-600">Home</a> <span class="mx-2 text-gray-400">/</span> <a${addAttribute(`/category/${article.category.toLowerCase().replace(" ", "-")}`, "href")} class="text-gray-500 hover:text-primary-600">${article.category}</a> <span class="mx-2 text-gray-400">/</span> <span class="text-gray-700">${article.title}</span> </nav> </div> </div>  <header class="py-8 bg-white"> <div class="container mx-auto px-4"> <div class="max-w-4xl mx-auto"> <span class="inline-block bg-primary-100 text-primary-800 text-sm font-medium px-3 py-1 rounded-full mb-4"> ${article.category} </span> <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">${article.title}</h1> <p class="text-xl text-gray-600 mb-6">${article.excerpt}</p> <div class="flex items-center space-x-4 mb-6"> <img${addAttribute(article.authorImage, "src")}${addAttribute(article.author, "alt")} class="w-12 h-12 rounded-full object-cover"> <div> <div class="font-medium">${article.author}</div> <div class="text-sm text-gray-500">${article.publishedDate}</div> </div> </div> <div class="flex flex-wrap gap-2 mb-8"> ${article.tags.map((tag) => renderTemplate`<a${addAttribute(`/tag/${tag.toLowerCase().replace(" ", "-")}`, "href")} class="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm px-3 py-1 rounded-full transition-colors"> ${tag} </a>`)} </div> </div> </div> </header>  <div class="container mx-auto px-4 mb-10"> <div class="max-w-4xl mx-auto"> <div class="relative pb-[50%] overflow-hidden rounded-xl"> <img${addAttribute(article.image, "src")}${addAttribute(article.title, "alt")} class="absolute inset-0 w-full h-full object-cover"> </div> </div> </div>  <div class="container mx-auto px-4 mb-16"> <div class="max-w-4xl mx-auto"> <article class="prose prose-lg max-w-none"> <div>${unescapeHTML(article.content)}</div> </article> <!-- 作者信息 --> <div class="mt-12 p-6 bg-gray-50 rounded-xl"> <div class="flex items-start space-x-4"> <img${addAttribute(article.authorImage, "src")}${addAttribute(article.author, "alt")} class="w-16 h-16 rounded-full object-cover"> <div> <h3 class="font-bold text-lg">About ${article.author}</h3> <p class="text-gray-600 mt-1">${article.authorBio}</p> </div> </div> </div> <!-- 分享按钮 --> <div class="mt-8 flex items-center justify-between"> <div class="text-gray-700 font-medium">Share this article:</div> <div class="flex space-x-4"> <a href="#" class="text-gray-500 hover:text-blue-600"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"></path> </svg> </a> <a href="#" class="text-gray-500 hover:text-blue-400"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path> </svg> </a> <a href="#" class="text-gray-500 hover:text-green-600"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd"></path> </svg> </a> <a href="#" class="text-gray-500 hover:text-blue-700"> <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd"></path> </svg> </a> </div> </div> </div> </div>  <section class="py-12 bg-gray-50"> <div class="container mx-auto px-4 max-w-5xl"> <h2 class="text-3xl font-bold mb-8 text-center">Related Articles</h2> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> ${relatedArticles.map((article2) => renderTemplate`<article class="card h-full flex flex-col"> <div class="relative pb-[60%] overflow-hidden"> <img${addAttribute(article2.image, "src")}${addAttribute(article2.title, "alt")} class="absolute inset-0 w-full h-full object-cover transition-transform hover:scale-105"> <span class="absolute top-4 left-4 bg-primary-600 text-white text-sm font-medium px-3 py-1 rounded-full"> ${article2.category} </span> </div> <div class="p-6 flex-grow flex flex-col"> <h3 class="text-xl font-bold mb-3"> <a${addAttribute(`/article/${article2.slug}`, "href")} class="text-gray-900 hover:text-primary-600"> ${article2.title} </a> </h3> <p class="text-gray-600 mb-4 flex-grow">${article2.excerpt}</p> <div class="flex justify-between items-center text-sm text-gray-500"> <span>${article2.publishedDate}</span> <span>By ${article2.author}</span> </div> </div> </article>`)} </div> </div> </section>  <section class="py-16 bg-gradient-to-r from-primary-700 to-secondary-700 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h2 class="text-3xl md:text-4xl font-bold mb-6">Stay Updated with the Latest Reviews</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">Subscribe to our newsletter and never miss a new product review or buying guide.</p> <form class="max-w-xl mx-auto flex flex-col sm:flex-row gap-4"> <input type="email" placeholder="Your email address" class="flex-grow px-4 py-3 rounded-md focus:outline-none focus:ring-2 focus:ring-white text-gray-900" required> <button type="submit" class="bg-white text-primary-700 hover:bg-gray-100 px-6 py-3 rounded-md font-medium whitespace-nowrap">
Subscribe
</button> </form> </div> </section> ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/article/[slug].astro", void 0);

const $$file$1 = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/article/[slug].astro";
const $$url$1 = "/article/[slug]";

const _slug_$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug$1,
  file: $$file$1,
  getStaticPaths: getStaticPaths$1,
  url: $$url$1
}, Symbol.toStringTag, { value: 'Module' }));

const $$Astro = createAstro("https://smartreviews.com");
async function getStaticPaths() {
  const brands = [
    {
      id: 1,
      name: "Apple",
      logo: "/images/apple-logo.svg",
      slug: "apple",
      description: "Product reviews for Apple devices, including iPhone, iPad, MacBook, and Apple Watch.",
      articleCount: 32,
      featuredImage: "/images/brand-apple.jpg",
      website: "https://www.apple.com",
      founded: "1976",
      headquarters: "Cupertino, California, USA",
      overview: "Apple Inc. is an American multinational technology company that specializes in consumer electronics, software and online services. Its hardware products include the iPhone smartphone, iPad tablet computer, Mac personal computer, iPod portable media player, Apple Watch smartwatch, and Apple TV digital media player. Apple's software includes macOS, iOS, iPadOS, watchOS, and tvOS operating systems, as well as iTunes media player, Safari web browser, and various applications."
    },
    {
      id: 2,
      name: "Samsung",
      logo: "/images/samsung-logo.svg",
      slug: "samsung",
      description: "Reviews of Samsung electronics products, including Galaxy series phones, tablets, TVs, and home appliances.",
      articleCount: 28,
      featuredImage: "/images/brand-samsung.jpg",
      website: "https://www.samsung.com",
      founded: "1938",
      headquarters: "Seoul, South Korea",
      overview: "Samsung Group is South Korea's largest conglomerate, with businesses spanning electronics, finance, machinery, chemicals, and more. Samsung Electronics is the group's core company and a global leader in consumer electronics, semiconductors, and display technology manufacturing. Samsung Electronics' product line includes smartphones, tablets, TVs, monitors, storage devices, and home appliances."
    }
    // Other brand data...
  ];
  const brandArticles = {
    "apple": [
      {
        id: 1,
        title: "iPhone 15 Pro Max In-depth Review: Is Apple's New Flagship Worth Upgrading?",
        excerpt: "A comprehensive analysis of the iPhone 15 Pro Max's performance, camera, battery life, and new features to help you decide if it's worth upgrading from an older iPhone.",
        image: "/images/iphone-15-pro.jpg",
        category: "Smartphones",
        brand: "Apple",
        publishedDate: "October 20, 2023",
        author: "Michael Zhang",
        slug: "iphone-15-pro-max-review",
        rating: 4.5
      },
      {
        id: 2,
        title: "MacBook Air M3 Review: The New Standard for Ultrabooks",
        excerpt: "How does the MacBook Air with M3 chip perform? Is it the best ultrabook laptop available today?",
        image: "/images/macbook-air-m3.jpg",
        category: "Laptops",
        brand: "Apple",
        publishedDate: "October 15, 2023",
        author: "Lisa Wang",
        slug: "macbook-air-m3-review",
        rating: 4.8
      },
      {
        id: 3,
        title: "Apple Watch Series 9 Review: Small Improvements, Big Impact",
        excerpt: "What new features does the Apple Watch Series 9 bring? How does it differ from the previous generation?",
        image: "/images/apple-watch-series-9.jpg",
        category: "Wearables",
        brand: "Apple",
        publishedDate: "October 10, 2023",
        author: "Fiona Chen",
        slug: "apple-watch-series-9-review",
        rating: 4.3
      },
      {
        id: 4,
        title: "iPad Pro 2023 Review: The Perfect Blend of Productivity and Creativity",
        excerpt: "The new iPad Pro with M2 chip offers laptop-level performance. Can it replace your notebook?",
        image: "/images/ipad-pro-2023.jpg",
        category: "Tablets",
        brand: "Apple",
        publishedDate: "October 5, 2023",
        author: "Michael Zhang",
        slug: "ipad-pro-2023-review",
        rating: 4.6
      },
      {
        id: 5,
        title: "AirPods Pro 2 Review: Noise Cancellation Reaches New Heights",
        excerpt: "How do the AirPods Pro 2 perform in terms of noise cancellation, sound quality, and battery life? Are they worth upgrading from the first generation?",
        image: "/images/airpods-pro-2.jpg",
        category: "Audio",
        brand: "Apple",
        publishedDate: "September 30, 2023",
        author: "Lisa Wang",
        slug: "airpods-pro-2-review",
        rating: 4.7
      },
      {
        id: 6,
        title: "Mac Mini M2 Pro Review: Small Form Factor, Massive Power",
        excerpt: "The Mac Mini with M2 Pro chip delivers a performance leap. Is it the most cost-effective Mac computer?",
        image: "/images/mac-mini-m2-pro.jpg",
        category: "Computers",
        brand: "Apple",
        publishedDate: "September 25, 2023",
        author: "Fiona Chen",
        slug: "mac-mini-m2-pro-review",
        rating: 4.4
      }
    ],
    "samsung": [
      {
        id: 7,
        title: "Samsung Galaxy S23 Ultra Review: New Heights for Android Flagships",
        excerpt: "What innovations does the Galaxy S23 Ultra bring? How does it perform in terms of camera, performance, and battery life?",
        image: "/images/galaxy-s23-ultra.jpg",
        category: "Smartphones",
        brand: "Samsung",
        publishedDate: "October 18, 2023",
        author: "Michael Zhang",
        slug: "galaxy-s23-ultra-review",
        rating: 4.6
      },
      {
        id: 8,
        title: "Samsung Galaxy Z Fold 5 Review: The Evolution of Foldable Phones",
        excerpt: "What issues from previous generations does the Galaxy Z Fold 5 solve? Is foldable screen technology mature enough now?",
        image: "/images/galaxy-z-fold-5.jpg",
        category: "Smartphones",
        brand: "Samsung",
        publishedDate: "October 12, 2023",
        author: "Lisa Wang",
        slug: "galaxy-z-fold-5-review",
        rating: 4.2
      }
    ]
    // Other brand articles...
  };
  return brands.map((brand) => ({
    params: { slug: brand.slug },
    props: {
      brand,
      articles: brandArticles[brand.slug] || []
    }
  }));
}
const $$slug = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { brand, articles } = Astro2.props;
  const articlesByCategory = {};
  articles.forEach((article) => {
    if (!articlesByCategory[article.category]) {
      articlesByCategory[article.category] = [];
    }
    articlesByCategory[article.category].push(article);
  });
  const categories = Object.keys(articlesByCategory).sort();
  const relatedBrands = [
    { id: 3, name: "Sony", logo: "/images/sony-logo.svg", slug: "sony" },
    { id: 4, name: "Microsoft", logo: "/images/microsoft-logo.svg", slug: "microsoft" },
    { id: 6, name: "Google", logo: "/images/google-logo.svg", slug: "google" }
  ];
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": `${brand.name} Product Reviews | SmartReviews`, "description": brand.description, "image": brand.featuredImage }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="relative"> <div class="w-full h-64 md:h-80 relative overflow-hidden"> <img${addAttribute(brand.banner || "/images/default-brand-banner.jpg", "src")}${addAttribute(brand.name, "alt")} class="w-full h-full object-cover"> <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent"></div> </div> <div class="absolute inset-0 flex items-end"> <div class="container mx-auto px-4 max-w-5xl pb-8"> <nav class="flex text-sm mb-4 text-gray-300"> <a href="/" class="hover:text-white">Home</a> <span class="mx-2">/</span> <a href="/brands" class="hover:text-white">Brands</a> <span class="mx-2">/</span> <span class="text-white">${brand.name}</span> </nav> <div class="flex items-center gap-6"> <div class="w-20 h-20 bg-white rounded-lg p-2 flex items-center justify-center"> <img${addAttribute(brand.logo, "src")}${addAttribute(`${brand.name} logo`, "alt")} class="max-h-full max-w-full"> </div> <div> <h1 class="text-4xl md:text-5xl font-bold text-white mb-2">${brand.name}</h1> <p class="text-xl text-gray-200 max-w-3xl">${brand.description}</p> </div> </div> </div> </div> </div>  <section class="py-12 bg-white"> <div class="container mx-auto px-4 max-w-5xl"> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> <div class="card p-6"> <h3 class="font-bold text-gray-500 mb-2">Founded</h3> <p class="text-xl">${brand.founded}</p> </div> <div class="card p-6"> <h3 class="font-bold text-gray-500 mb-2">Headquarters</h3> <p class="text-xl">${brand.headquarters}</p> </div> <div class="card p-6"> <h3 class="font-bold text-gray-500 mb-2">Official Website</h3> <a${addAttribute(brand.website, "href")} target="_blank" rel="noopener noreferrer" class="text-xl text-primary-600 hover:underline">${brand.website.replace("https://", "")}</a> </div> </div> <div class="card p-6"> <p class="text-gray-700 leading-relaxed">${brand.overview}</p> </div> </div> </section>  <section class="py-12 bg-gray-50"> <div class="container mx-auto px-4 max-w-5xl"> <h2 class="text-3xl font-bold mb-8">Latest ${brand.name} Product Reviews</h2> ${categories.length > 0 ? renderTemplate`<div class="space-y-12"> ${categories.map((category) => renderTemplate`<div> <h3 class="text-2xl font-bold mb-6 pb-2 border-b border-gray-200">${category}</h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> ${articlesByCategory[category].map((article) => renderTemplate`<article class="card overflow-hidden hover:shadow-md transition-shadow"> <div class="relative pb-[60%] overflow-hidden"> <img${addAttribute(article.image, "src")}${addAttribute(article.title, "alt")} class="absolute inset-0 w-full h-full object-cover transition-transform hover:scale-105"> <div class="absolute top-4 left-4 flex space-x-2"> <span class="bg-primary-600 text-white text-sm font-medium px-3 py-1 rounded-full"> ${article.category} </span> ${article.rating && renderTemplate`<span class="bg-accent-400 text-gray-900 text-sm font-medium px-3 py-1 rounded-full flex items-center"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor"> <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path> </svg> ${article.rating} </span>`} </div> </div> <div class="p-6"> <h4 class="text-xl font-bold mb-3"> <a${addAttribute(`/article/${article.slug}`, "href")} class="text-gray-900 hover:text-primary-600"> ${article.title} </a> </h4> <p class="text-gray-600 mb-4">${article.excerpt}</p> <div class="flex justify-between items-center text-sm text-gray-500"> <span>${article.publishedDate}</span> <span>By ${article.author}</span> </div> </div> </article>`)} </div> </div>`)} </div>` : renderTemplate`<div class="text-center py-12"> <p class="text-gray-500 text-lg">No articles available</p> </div>`} </div> </section>  <section class="py-12 bg-white"> <div class="container mx-auto px-4 max-w-5xl"> <h2 class="text-3xl font-bold mb-8">Related Brands</h2> <div class="grid grid-cols-2 sm:grid-cols-3 gap-6"> ${relatedBrands.map((relBrand) => renderTemplate`<a${addAttribute(`/brand/${relBrand.slug}`, "href")} class="card p-6 hover:shadow-md transition-shadow flex flex-col items-center text-center"> <img${addAttribute(relBrand.logo, "src")}${addAttribute(`${relBrand.name} logo`, "alt")} class="w-16 h-16 object-contain mb-4"> <h3 class="text-xl font-bold text-gray-900">${relBrand.name}</h3> </a>`)} </div> </div> </section>  <section class="py-12 bg-gray-50"> <div class="container mx-auto px-4 max-w-5xl"> <h2 class="text-3xl font-bold mb-8 text-center">品牌对比 <span class="text-primary-600">省钱攻略</span></h2> <p class="text-xl text-gray-600 text-center mb-10">对比${brand.name}产品与其他品牌，找到最适合您的选择</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <a${addAttribute(`/comparison/${brand.slug}-vs-samsung`, "href")} class="card p-6 hover:shadow-md transition-shadow border-l-4 border-primary-500"> <div class="flex justify-between items-center mb-4"> <div class="flex items-center"> <img${addAttribute(brand.logo, "src")}${addAttribute(`${brand.name} logo`, "alt")} class="w-10 h-10 object-contain mr-2"> <span class="text-lg font-medium text-secondary-600">vs</span> <img src="/images/samsung-logo.svg" alt="Samsung logo" class="w-10 h-10 object-contain ml-2"> </div> <span class="affiliate-badge">佣金高达8%</span> </div> <h3 class="text-xl font-bold mb-2">${brand.name} vs Samsung</h3> <p class="text-gray-600">从智能手机到平板电脑，笔记本电脑到智能手表，全方位对比两大科技巨头的产品。</p> <div class="mt-4 flex justify-between items-center"> <span class="text-sm text-primary-600">查看详细对比</span> <span class="text-sm bg-accent-100 text-accent-800 px-2 py-1 rounded">热门对比</span> </div> </a> <a${addAttribute(`/comparison/${brand.slug}-vs-google`, "href")} class="card p-6 hover:shadow-md transition-shadow border-l-4 border-primary-500"> <div class="flex justify-between items-center mb-4"> <div class="flex items-center"> <img${addAttribute(brand.logo, "src")}${addAttribute(`${brand.name} logo`, "alt")} class="w-10 h-10 object-contain mr-2"> <span class="text-lg font-medium text-secondary-600">vs</span> <img src="/images/google-logo.svg" alt="Google logo" class="w-10 h-10 object-contain ml-2"> </div> <span class="affiliate-badge">佣金高达6%</span> </div> <h3 class="text-xl font-bold mb-2">${brand.name} vs Google</h3> <p class="text-gray-600">哪家创新公司拥有更好的生态系统？在我们的详细比较中找到答案。</p> <div class="mt-4 flex justify-between items-center"> <span class="text-sm text-primary-600">查看详细对比</span> <span class="text-sm bg-secondary-100 text-secondary-800 px-2 py-1 rounded">新品对比</span> </div> </a> </div> </div> </section>  <section class="py-16 bg-gradient-to-r from-primary-700 to-secondary-700 text-white"> <div class="container mx-auto px-4 max-w-5xl text-center"> <h2 class="text-3xl md:text-4xl font-bold mb-6">Stay Updated on ${brand.name}</h2> <p class="text-xl mb-8 max-w-3xl mx-auto">订阅我们的通讯，获取最新${brand.name}产品评测、购买指南和<span class="text-accent-300 font-bold">独家折扣码</span>！</p> <div class="mb-8 bg-white/10 rounded-lg p-4 max-w-3xl mx-auto"> <div class="flex items-center justify-center mb-4"> <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> <span class="text-lg font-medium">订阅即可获得价值¥50的优惠券</span> </div> </div> <form class="max-w-xl mx-auto flex flex-col sm:flex-row gap-4"> <input type="email" placeholder="您的电子邮箱地址" class="flex-grow px-4 py-3 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-300 text-gray-900" required> <button type="submit" class="bg-accent-500 text-white hover:bg-accent-600 px-6 py-3 rounded-md font-medium whitespace-nowrap transition-colors">
立即订阅
</button> </form> </div> </section> ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/brand/[slug].astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/brand/[slug].astro";
const $$url = "/brand/[slug]";

const _slug_ = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

export { $$MainLayout as $, _slug_$1 as _, _slug_ as a, siteConfig as s };
