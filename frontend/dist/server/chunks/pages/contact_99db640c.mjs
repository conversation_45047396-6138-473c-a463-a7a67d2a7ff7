/* empty css                           */import { e as createComponent, r as renderTemplate, j as renderComponent, m as maybeRenderHead, f as addAttribute, F as Fragment, u as unescapeHTML } from '../astro_0d22710d.mjs';
import { $ as $$MainLayout, s as siteConfig } from './_slug__fe545221.mjs';

const $$Contact = createComponent(($$result, $$props, $$slots) => {
  const { containerWidth } = siteConfig;
  const contactMethods = [
    {
      title: "Email Us",
      description: "For general inquiries, feedback, or business opportunities",
      value: "<EMAIL>",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>`
    },
    {
      title: "Media Inquiries",
      description: "For press related questions and interview requests",
      value: "<EMAIL>",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" /></svg>`
    },
    {
      title: "Partnership Opportunities",
      description: "For brand collaborations and affiliate inquiries",
      value: "<EMAIL>",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>`
    },
    {
      title: "Phone",
      description: "Monday-Friday, 9AM-5PM (Pacific Time)",
      value: "+1 (800) 555-TECH",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" /></svg>`
    },
    {
      title: "Office Address",
      description: "Our headquarters location",
      value: "1234 Tech Boulevard, San Francisco, CA 94107",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`
    },
    {
      title: "Social Media",
      description: "Follow us for updates and tech news",
      value: "@SmartReviewsTech",
      icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" /></svg>`
    }
  ];
  const faqItems = [
    {
      question: "How can I submit a product for review?",
      answer: "If you're a manufacturer or brand representative interested in having us review your product, please email <NAME_EMAIL> with details about your product. While we can't guarantee coverage, we review all submissions and select products based on our editorial calendar and reader interest."
    },
    {
      question: "Do you accept guest posts or contributed content?",
      answer: "We occasionally publish expert-written content from industry professionals. If you're interested in contributing, <NAME_EMAIL> with your proposed topic and writing samples. Note that we maintain strict editorial standards and do not publish promotional content."
    },
    {
      question: "How can I report an error in a review?",
      answer: "We strive for accuracy in all our content. If you believe you've found an error in one of our reviews or articles, please let us know <NAME_EMAIL> with the article URL and a description of the issue."
    },
    {
      question: "Can I interview someone from SmartReviews for my publication?",
      answer: "For media inquiries, including interview requests with our team members, please contact our press <NAME_EMAIL> with details about your publication and the topic you'd like to discuss."
    }
  ];
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Contact Us | SmartReviews", "description": "Get in touch with the SmartReviews team for inquiries, feedback, partnership opportunities, or media requests." }, { "default": ($$result2) => renderTemplate`  ${maybeRenderHead()}<header class="bg-gradient-to-r from-purple-700 to-purple-800 text-white py-16"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="max-w-3xl mx-auto text-center"> <h1 class="text-4xl md:text-5xl font-bold mb-6">Contact Us</h1> <p class="text-xl mb-8">
Have a question, suggestion, or partnership opportunity? We'd love to hear from you.
</p> </div> </div> </header>  <section class="py-16 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="max-w-5xl mx-auto"> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${contactMethods.map((method) => renderTemplate`<div class="bg-gray-50 p-6 rounded-lg shadow-sm"> <div class="text-purple-600 mb-4"> ${renderComponent($$result2, "Fragment", Fragment, {}, { "default": ($$result3) => renderTemplate`${unescapeHTML(method.icon)}` })} </div> <h3 class="text-lg font-bold mb-2">${method.title}</h3> <p class="text-gray-600 text-sm mb-3">${method.description}</p> <p class="font-medium">${method.value}</p> </div>`)} </div> </div> </div> </section>  <section class="py-16 bg-gray-50"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-5xl mx-auto"> <div> <h2 class="text-3xl font-bold mb-6">Send Us a Message</h2> <p class="text-gray-600 mb-8">
Fill out the form below and we'll get back to you as soon as possible. For faster responses to common questions, check out our FAQ section.
</p> <form class="space-y-6"> <div> <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Your Name</label> <input type="text" id="name" name="name" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"> </div> <div> <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label> <input type="email" id="email" name="email" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"> </div> <div> <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label> <select id="subject" name="subject" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"> <option value="">Select a subject</option> <option value="general">General Inquiry</option> <option value="feedback">Feedback on a Review</option> <option value="correction">Report an Error</option> <option value="partnership">Partnership Opportunity</option> <option value="media">Media Inquiry</option> <option value="other">Other</option> </select> </div> <div> <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Your Message</label> <textarea id="message" name="message" rows="6" required class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"></textarea> </div> <div class="flex items-start"> <div class="flex items-center h-5"> <input id="privacy" type="checkbox" required class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"> </div> <div class="ml-3 text-sm"> <label for="privacy" class="font-medium text-gray-700">
I agree to the <a href="/privacy" class="text-purple-600 hover:text-purple-800">Privacy Policy</a> </label> </div> </div> <div> <button type="submit" class="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 transition-colors font-medium">
Send Message
</button> </div> </form> </div> <div> <h2 class="text-3xl font-bold mb-6">Frequently Asked Questions</h2> <div class="space-y-6"> ${faqItems.map((item) => renderTemplate`<div class="bg-white p-6 rounded-lg shadow-sm"> <h3 class="text-lg font-bold mb-3">${item.question}</h3> <p class="text-gray-600">${item.answer}</p> </div>`)} </div> </div> </div> </div> </section>  <section class="py-16 bg-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="max-w-5xl mx-auto"> <h2 class="text-3xl font-bold mb-8 text-center">Visit Our Office</h2> <div class="bg-gray-200 rounded-xl overflow-hidden h-[400px] shadow-md"> <!-- Replace with actual Google Maps embed or other map provider --> <div class="w-full h-full"> <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.1332520612557!2d-122.4023694!3d37.7849!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80858075e0e14357%3A0xfb366295208bc705!2sSan%20Francisco%2C%20CA%2094107!5e0!3m2!1sen!2sus!4v1620000000000!5m2!1sen!2sus" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"></iframe> </div> </div> <div class="mt-8 text-center"> <p class="text-gray-600"> <strong>SmartReviews Headquarters</strong><br>
1234 Tech Boulevard, San Francisco, CA 94107<br>
Operating Hours: Monday-Friday, 9AM-5PM (Pacific Time)
</p> </div> </div> </div> </section>  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white"> <div${addAttribute(`container mx-auto px-4 ${containerWidth}`, "class")}> <div class="max-w-xl mx-auto text-center"> <h2 class="text-3xl font-bold mb-6">Stay Updated</h2> <p class="text-xl mb-8">
Subscribe to our newsletter to receive the latest reviews, buying guides, and tech news directly in your inbox.
</p> <form class="flex flex-col sm:flex-row gap-4"> <input type="email" placeholder="Your email address" class="px-4 py-3 rounded-md flex-grow text-gray-900" required> <button type="submit" class="bg-purple-600 hover:bg-purple-700 border border-purple-500 px-6 py-3 rounded-md font-medium transition-colors whitespace-nowrap">
Subscribe
</button> </form> <p class="mt-4 text-sm opacity-80">
We respect your privacy. Unsubscribe at any time.
</p> </div> </div> </section> ` })}`;
}, "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/contact.astro", void 0);

const $$file = "/Users/<USER>/projects/reviews-site/smartreviews/src/pages/contact.astro";
const $$url = "/contact";

export { $$Contact as default, $$file as file, $$url as url };
