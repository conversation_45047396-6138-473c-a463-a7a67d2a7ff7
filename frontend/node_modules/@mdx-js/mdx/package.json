{"name": "@mdx-js/mdx", "version": "2.3.0", "description": "MDX compiler", "license": "MIT", "keywords": ["mdx", "markdown", "jsx", "remark", "mdxast"], "homepage": "https://mdxjs.com", "repository": {"type": "git", "url": "https://github.com/mdx-js/mdx", "directory": "packages/mdx"}, "bugs": "https://github.com/mdx-js/mdx/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://johno.com)", "contributors": ["<PERSON> <<EMAIL>> (https://johno.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <matija.ma<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stg.me)", "<PERSON> <<EMAIL>>"], "type": "module", "sideEffects": false, "main": "index.js", "types": "index.d.ts", "browser": {"./lib/condition.js": "./lib/condition.browser.js"}, "react-native": {"./lib/condition.js": "./lib/condition.browser.js"}, "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/estree-jsx": "^1.0.0", "@types/mdx": "^2.0.0", "estree-util-build-jsx": "^2.0.0", "estree-util-is-identifier-name": "^2.0.0", "estree-util-to-js": "^1.1.0", "estree-walker": "^3.0.0", "hast-util-to-estree": "^2.0.0", "markdown-extensions": "^1.0.0", "periscopic": "^3.0.0", "remark-mdx": "^2.0.0", "remark-parse": "^10.0.0", "remark-rehype": "^10.0.0", "unified": "^10.0.0", "unist-util-position-from-estree": "^1.0.0", "unist-util-stringify-position": "^3.0.0", "unist-util-visit": "^4.0.0", "vfile": "^5.0.0"}, "devDependencies": {"@emotion/react": "^11.0.0", "@mdx-js/react": "^2.0.0", "nanoid": "^4.0.0", "preact": "^10.0.0", "preact-render-to-string": "^5.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "rehype-katex": "^6.0.0", "rehype-raw": "^6.0.0", "remark-frontmatter": "^4.0.0", "remark-gfm": "^3.0.0", "remark-math": "^5.0.0", "source-map": "^0.7.0", "source-map-support": "^0.5.0", "unist-util-remove-position": "^4.0.0"}, "scripts": {"prepack": "npm run build", "build": "tsc --build --clean && tsc --build && type-coverage", "test-api": "uvu test \"^(compile|evaluate)\\.js$\"", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run test-coverage"}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true, "ignoreFiles": ["lib/util/resolve-evaluate-options.{d.ts,js}"]}, "gitHead": "bf7deab69996449cb99c2217dff75e65855eb2c1"}