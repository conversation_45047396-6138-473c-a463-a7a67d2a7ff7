{"name": "@astrojs/markdown-remark", "version": "3.5.0", "type": "module", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/withastro/astro.git", "directory": "packages/markdown/remark"}, "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://astro.build", "main": "./dist/index.js", "exports": {".": "./dist/index.js", "./dist/internal.js": "./dist/internal.js"}, "files": ["dist"], "peerDependencies": {"astro": "^3.0.0"}, "dependencies": {"@astrojs/prism": "^3.0.0", "github-slugger": "^2.0.0", "import-meta-resolve": "^3.0.0", "mdast-util-definitions": "^6.0.0", "rehype-raw": "^6.1.1", "rehype-stringify": "^9.0.4", "remark-gfm": "^3.0.1", "remark-parse": "^10.0.2", "remark-rehype": "^10.1.0", "remark-smartypants": "^2.0.0", "shikiji": "^0.6.8", "unified": "^10.1.2", "unist-util-visit": "^4.1.2", "vfile": "^5.3.7"}, "devDependencies": {"@types/chai": "^4.3.5", "@types/estree": "^1.0.1", "@types/hast": "^2.3.5", "@types/mdast": "^3.0.12", "@types/mocha": "^10.0.1", "@types/unist": "^2.0.7", "chai": "^4.3.7", "mdast-util-mdx-expression": "^1.3.2", "mocha": "^10.2.0", "astro-scripts": "0.0.14"}, "publishConfig": {"provenance": true}, "scripts": {"prepublish": "pnpm build", "build": "astro-scripts build \"src/**/*.ts\" && tsc -p tsconfig.json", "build:ci": "astro-scripts build \"src/**/*.ts\"", "postbuild": "astro-scripts copy \"src/**/*.js\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "mocha --exit --timeout 20000"}}