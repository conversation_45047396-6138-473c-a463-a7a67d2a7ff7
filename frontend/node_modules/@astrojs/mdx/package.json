{"name": "@astrojs/mdx", "description": "Add support for MDX pages in your Astro site", "version": "1.1.5", "type": "module", "types": "./dist/index.d.ts", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/withastro/astro.git", "directory": "packages/integrations/mdx"}, "keywords": ["astro-integration", "astro-component", "mdx"], "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://docs.astro.build/en/guides/integrations-guide/mdx/", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist", "template"], "dependencies": {"@mdx-js/mdx": "^2.3.0", "acorn": "^8.10.0", "es-module-lexer": "^1.3.0", "estree-util-visit": "^1.2.1", "github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "hast-util-to-html": "^8.0.4", "kleur": "^4.1.4", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "remark-smartypants": "^2.0.0", "source-map": "^0.7.4", "unist-util-visit": "^4.1.2", "vfile": "^5.3.7", "@astrojs/markdown-remark": "3.5.0"}, "peerDependencies": {"astro": "^3.0.0"}, "devDependencies": {"@types/chai": "^4.3.5", "@types/estree": "^1.0.1", "@types/mdast": "^3.0.12", "@types/mocha": "^10.0.1", "@types/yargs-parser": "^21.0.0", "chai": "^4.3.7", "cheerio": "1.0.0-rc.12", "linkedom": "^0.15.1", "mdast-util-mdx": "^2.0.1", "mdast-util-to-string": "^3.2.0", "mocha": "^10.2.0", "reading-time": "^1.5.0", "rehype-mathjax": "^4.0.3", "rehype-pretty-code": "^0.10.0", "remark-math": "^5.1.1", "remark-rehype": "^10.1.0", "remark-shiki-twoslash": "^3.1.3", "remark-toc": "^8.0.1", "unified": "^10.1.2", "vite": "^4.4.9", "astro": "3.5.4", "astro-scripts": "0.0.14"}, "engines": {"node": ">=18.14.1"}, "publishConfig": {"provenance": true}, "scripts": {"build": "astro-scripts build \"src/**/*.ts\" && tsc", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "mocha --exit --timeout 20000", "test:match": "mocha --timeout 20000 -g"}}