export interface OptimizeOptions {
    customComponentNames?: string[];
}
/**
 * For MDX only, collapse static subtrees of the hast into `set:html`. Subtrees
 * do not include any MDX elements.
 *
 * This optimization reduces the JS output as more content are represented as a
 * string instead, which also reduces the AST size that <PERSON><PERSON> holds in memory.
 */
export declare function rehypeOptimizeStatic(options?: OptimizeOptions): (tree: any) => void;
