"use strict";var o=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var c=Object.prototype.hasOwnProperty;var l=(r,t)=>{for(var n in t)o(r,n,{get:t[n],enumerable:!0})},g=(r,t,n,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let e of p(t))!c.call(r,e)&&e!==n&&o(r,e,{get:()=>t[e],enumerable:!(s=a(t,e))||s.enumerable});return r};var d=r=>g(o({},"__esModule",{value:!0}),r);var m={};l(m,{DiagnosticSeverity:()=>i});module.exports=d(m);var i=(e=>(e[e.Error=1]="Error",e[e.Warning=2]="Warning",e[e.Information=3]="Information",e[e.Hint=4]="Hint",e))(i||{});0&&(module.exports={DiagnosticSeverity});
