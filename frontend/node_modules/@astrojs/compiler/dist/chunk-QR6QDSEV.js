var l=()=>{let r=new Error("not implemented");return r.code="ENOSYS",r},g="",w={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync(r,n){g+=x.decode(n);let s=g.lastIndexOf(`
`);return s!=-1&&(console.log(g.substr(0,s)),g=g.substr(s+1)),n.length},write(r,n,s,i,h,c){if(s!==0||i!==n.length||h!==null){c(l());return}let d=this.writeSync(r,n);c(null,d)},chmod(r,n,s){s(l())},chown(r,n,s,i){i(l())},close(r,n){n(l())},fchmod(r,n,s){s(l())},fchown(r,n,s,i){i(l())},fstat(r,n){n(l())},fsync(r,n){n(null)},ftruncate(r,n,s){s(l())},lchown(r,n,s,i){i(l())},link(r,n,s){s(l())},lstat(r,n){n(l())},mkdir(r,n,s){s(l())},open(r,n,s,i){i(l())},read(r,n,s,i,h,c){c(l())},readdir(r,n){n(l())},readlink(r,n){n(l())},rename(r,n,s){s(l())},rmdir(r,n){n(l())},stat(r,n){n(l())},symlink(r,n,s){s(l())},truncate(r,n,s){s(l())},unlink(r,n){n(l())},utimes(r,n,s,i){i(l())}},b={getuid(){return-1},getgid(){return-1},geteuid(){return-1},getegid(){return-1},getgroups(){throw l()},pid:-1,ppid:-1,umask(){throw l()},cwd(){throw l()},chdir(){throw l()}};Object.defineProperties(globalThis,{fs:{value:w,enumerable:!0},process:{value:b,enumerable:!0}});var y=new TextEncoder("utf-8"),x=new TextDecoder("utf-8");var _=class{constructor(){this.argv=["js"],this.env={},this.exit=t=>{t!==0&&console.warn("exit code:",t)},this._exitPromise=new Promise(t=>{this._resolveExitPromise=t}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;let n=(t,e)=>{this.mem.setUint32(t+0,e,!0),this.mem.setUint32(t+4,Math.floor(e/4294967296),!0)},s=t=>{let e=this.mem.getUint32(t+0,!0),o=this.mem.getInt32(t+4,!0);return e+o*4294967296},i=t=>{let e=this.mem.getFloat64(t,!0);if(e===0)return;if(!isNaN(e))return e;let o=this.mem.getUint32(t,!0);return this._values[o]},h=(t,e)=>{if(typeof e=="number"&&e!==0){if(isNaN(e)){this.mem.setUint32(t+4,2146959360,!0),this.mem.setUint32(t,0,!0);return}this.mem.setFloat64(t,e,!0);return}if(e===void 0){this.mem.setFloat64(t,0,!0);return}let a=this._ids.get(e);a===void 0&&(a=this._idPool.pop(),a===void 0&&(a=this._values.length),this._values[a]=e,this._goRefCounts[a]=0,this._ids.set(e,a)),this._goRefCounts[a]++;let u=0;switch(typeof e){case"object":e!==null&&(u=1);break;case"string":u=2;break;case"symbol":u=3;break;case"function":u=4;break}this.mem.setUint32(t+4,2146959360|u,!0),this.mem.setUint32(t,a,!0)},c=t=>{let e=s(t+0),o=s(t+8);return new Uint8Array(this._inst.exports.mem.buffer,e,o)},d=t=>{let e=s(t+0),o=s(t+8),a=new Array(o);for(let u=0;u<o;u++)a[u]=i(e+u*8);return a},f=t=>{let e=s(t+0),o=s(t+8);return x.decode(new DataView(this._inst.exports.mem.buffer,e,o))},m=Date.now()-performance.now();this.importObject={gojs:{"runtime.wasmExit":t=>{t>>>=0;let e=this.mem.getInt32(t+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(e)},"runtime.wasmWrite":t=>{t>>>=0;let e=s(t+8),o=s(t+16),a=this.mem.getInt32(t+24,!0);w.writeSync(e,new Uint8Array(this._inst.exports.mem.buffer,o,a))},"runtime.resetMemoryDataView":t=>{t>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":t=>{t>>>=0,n(t+8,(m+performance.now())*1e6)},"runtime.walltime":t=>{t>>>=0;let e=new Date().getTime();n(t+8,e/1e3),this.mem.setInt32(t+16,e%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":t=>{t>>>=0;let e=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(e,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(e);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},s(t+8)+1)),this.mem.setInt32(t+16,e,!0)},"runtime.clearTimeoutEvent":t=>{t>>>=0;let e=this.mem.getInt32(t+8,!0);clearTimeout(this._scheduledTimeouts.get(e)),this._scheduledTimeouts.delete(e)},"runtime.getRandomData":t=>{t>>>=0,globalThis.crypto.getRandomValues(c(t+8))},"syscall/js.finalizeRef":t=>{t>>>=0;let e=this.mem.getUint32(t+8,!0);if(this._goRefCounts[e]--,this._goRefCounts[e]===0){let o=this._values[e];this._values[e]=null,this._ids.delete(o),this._idPool.push(e)}},"syscall/js.stringVal":t=>{t>>>=0,h(t+24,f(t+8))},"syscall/js.valueGet":t=>{t>>>=0;let e=Reflect.get(i(t+8),f(t+16));t=this._inst.exports.getsp()>>>0,h(t+32,e)},"syscall/js.valueSet":t=>{t>>>=0,Reflect.set(i(t+8),f(t+16),i(t+32))},"syscall/js.valueDelete":t=>{t>>>=0,Reflect.deleteProperty(i(t+8),f(t+16))},"syscall/js.valueIndex":t=>{t>>>=0,h(t+24,Reflect.get(i(t+8),s(t+16)))},"syscall/js.valueSetIndex":t=>{t>>>=0,Reflect.set(i(t+8),s(t+16),i(t+24))},"syscall/js.valueCall":t=>{t>>>=0;try{let e=i(t+8),o=Reflect.get(e,f(t+16)),a=d(t+32),u=Reflect.apply(o,e,a);t=this._inst.exports.getsp()>>>0,h(t+56,u),this.mem.setUint8(t+64,1)}catch(e){t=this._inst.exports.getsp()>>>0,h(t+56,e),this.mem.setUint8(t+64,0)}},"syscall/js.valueInvoke":t=>{t>>>=0;try{let e=i(t+8),o=d(t+16),a=Reflect.apply(e,void 0,o);t=this._inst.exports.getsp()>>>0,h(t+40,a),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,h(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueNew":t=>{t>>>=0;try{let e=i(t+8),o=d(t+16),a=Reflect.construct(e,o);t=this._inst.exports.getsp()>>>0,h(t+40,a),this.mem.setUint8(t+48,1)}catch(e){t=this._inst.exports.getsp()>>>0,h(t+40,e),this.mem.setUint8(t+48,0)}},"syscall/js.valueLength":t=>{t>>>=0,n(t+16,Number.parseInt(i(t+8).length))},"syscall/js.valuePrepareString":t=>{t>>>=0;let e=y.encode(String(i(t+8)));h(t+16,e),n(t+24,e.length)},"syscall/js.valueLoadString":t=>{t>>>=0;let e=i(t+8);c(t+16).set(e)},"syscall/js.valueInstanceOf":t=>{t>>>=0,this.mem.setUint8(t+24,i(t+8)instanceof i(t+16)?1:0)},"syscall/js.copyBytesToGo":t=>{t>>>=0;let e=c(t+8),o=i(t+32);if(!(o instanceof Uint8Array||o instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}let a=o.subarray(0,e.length);e.set(a),n(t+40,a.length),this.mem.setUint8(t+48,1)},"syscall/js.copyBytesToJS":t=>{t>>>=0;let e=i(t+8),o=c(t+16);if(!(e instanceof Uint8Array||e instanceof Uint8ClampedArray)){this.mem.setUint8(t+48,0);return}let a=o.subarray(0,e.length);e.set(a),n(t+40,a.length),this.mem.setUint8(t+48,1)},debug:t=>{console.log(t)}}}}async run(n){if(!(n instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=n,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[Number.NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(Number.POSITIVE_INFINITY),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let s=4096,i=m=>{let t=s,e=y.encode(`${m}\0`);return new Uint8Array(this.mem.buffer,s,e.length).set(e),s+=e.length,s%8!==0&&(s+=8-s%8),t},h=this.argv.length,c=[];this.argv.forEach(m=>{c.push(i(m))}),c.push(0),Object.keys(this.env).sort().forEach(m=>{c.push(i(`${m}=${this.env[m]}`))}),c.push(0);let f=s;c.forEach(m=>{this.mem.setUint32(s,m,!0),this.mem.setUint32(s+4,0,!0),s+=8}),this._inst.exports.run(h,f),this.exited&&this._resolveExitPromise(),await this._exitPromise}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(n){let s=this;return function(){let i={id:n,this:this,args:arguments};return s._pendingEvent=i,s._resume(),i.result}}};export{_ as a};
