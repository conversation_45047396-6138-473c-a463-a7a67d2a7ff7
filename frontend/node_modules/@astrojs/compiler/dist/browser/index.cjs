"use strict";var p=Object.defineProperty;var S=Object.getOwnPropertyDescriptor;var U=Object.getOwnPropertyNames;var I=Object.prototype.hasOwnProperty;var R=(i,s)=>{for(var n in s)p(i,n,{get:s[n],enumerable:!0})},j=(i,s,n,r)=>{if(s&&typeof s=="object"||typeof s=="function")for(let o of U(s))!I.call(i,o)&&o!==n&&p(i,o,{get:()=>s[o],enumerable:!(r=S(s,o))||r.enumerable});return i};var P=i=>j(p({},"__esModule",{value:!0}),i);var L={};R(L,{convertToTSX:()=>N,initialize:()=>D,parse:()=>O,teardown:()=>C,transform:()=>k});module.exports=P(L);var l=()=>{let i=new Error("not implemented");return i.code="ENOSYS",i},y="",x={constants:{O_WRONLY:-1,O_RDWR:-1,O_CREAT:-1,O_TRUNC:-1,O_APPEND:-1,O_EXCL:-1},writeSync(i,s){y+=T.decode(s);let n=y.lastIndexOf(`
`);return n!=-1&&(console.log(y.substr(0,n)),y=y.substr(n+1)),s.length},write(i,s,n,r,o,h){if(n!==0||r!==s.length||o!==null){h(l());return}let u=this.writeSync(i,s);h(null,u)},chmod(i,s,n){n(l())},chown(i,s,n,r){r(l())},close(i,s){s(l())},fchmod(i,s,n){n(l())},fchown(i,s,n,r){r(l())},fstat(i,s){s(l())},fsync(i,s){s(null)},ftruncate(i,s,n){n(l())},lchown(i,s,n,r){r(l())},link(i,s,n){n(l())},lstat(i,s){s(l())},mkdir(i,s,n){n(l())},open(i,s,n,r){r(l())},read(i,s,n,r,o,h){h(l())},readdir(i,s){s(l())},readlink(i,s){s(l())},rename(i,s,n){n(l())},rmdir(i,s){s(l())},stat(i,s){s(l())},symlink(i,s,n){n(l())},truncate(i,s,n){n(l())},unlink(i,s){s(l())},utimes(i,s,n,r){r(l())}},A={getuid(){return-1},getgid(){return-1},geteuid(){return-1},getegid(){return-1},getgroups(){throw l()},pid:-1,ppid:-1,umask(){throw l()},cwd(){throw l()},chdir(){throw l()}};Object.defineProperties(globalThis,{fs:{value:x,enumerable:!0},process:{value:A,enumerable:!0}});var b=new TextEncoder("utf-8"),T=new TextDecoder("utf-8");var w=class{constructor(){this.argv=["js"],this.env={},this.exit=e=>{e!==0&&console.warn("exit code:",e)},this._exitPromise=new Promise(e=>{this._resolveExitPromise=e}),this._pendingEvent=null,this._scheduledTimeouts=new Map,this._nextCallbackTimeoutID=1;let s=(e,t)=>{this.mem.setUint32(e+0,t,!0),this.mem.setUint32(e+4,Math.floor(t/4294967296),!0)},n=e=>{let t=this.mem.getUint32(e+0,!0),a=this.mem.getInt32(e+4,!0);return t+a*4294967296},r=e=>{let t=this.mem.getFloat64(e,!0);if(t===0)return;if(!isNaN(t))return t;let a=this.mem.getUint32(e,!0);return this._values[a]},o=(e,t)=>{if(typeof t=="number"&&t!==0){if(isNaN(t)){this.mem.setUint32(e+4,2146959360,!0),this.mem.setUint32(e,0,!0);return}this.mem.setFloat64(e,t,!0);return}if(t===void 0){this.mem.setFloat64(e,0,!0);return}let c=this._ids.get(t);c===void 0&&(c=this._idPool.pop(),c===void 0&&(c=this._values.length),this._values[c]=t,this._goRefCounts[c]=0,this._ids.set(t,c)),this._goRefCounts[c]++;let m=0;switch(typeof t){case"object":t!==null&&(m=1);break;case"string":m=2;break;case"symbol":m=3;break;case"function":m=4;break}this.mem.setUint32(e+4,2146959360|m,!0),this.mem.setUint32(e,c,!0)},h=e=>{let t=n(e+0),a=n(e+8);return new Uint8Array(this._inst.exports.mem.buffer,t,a)},u=e=>{let t=n(e+0),a=n(e+8),c=new Array(a);for(let m=0;m<a;m++)c[m]=r(t+m*8);return c},d=e=>{let t=n(e+0),a=n(e+8);return T.decode(new DataView(this._inst.exports.mem.buffer,t,a))},f=Date.now()-performance.now();this.importObject={gojs:{"runtime.wasmExit":e=>{e>>>=0;let t=this.mem.getInt32(e+8,!0);this.exited=!0,delete this._inst,delete this._values,delete this._goRefCounts,delete this._ids,delete this._idPool,this.exit(t)},"runtime.wasmWrite":e=>{e>>>=0;let t=n(e+8),a=n(e+16),c=this.mem.getInt32(e+24,!0);x.writeSync(t,new Uint8Array(this._inst.exports.mem.buffer,a,c))},"runtime.resetMemoryDataView":e=>{e>>>=0,this.mem=new DataView(this._inst.exports.mem.buffer)},"runtime.nanotime1":e=>{e>>>=0,s(e+8,(f+performance.now())*1e6)},"runtime.walltime":e=>{e>>>=0;let t=new Date().getTime();s(e+8,t/1e3),this.mem.setInt32(e+16,t%1e3*1e6,!0)},"runtime.scheduleTimeoutEvent":e=>{e>>>=0;let t=this._nextCallbackTimeoutID;this._nextCallbackTimeoutID++,this._scheduledTimeouts.set(t,setTimeout(()=>{for(this._resume();this._scheduledTimeouts.has(t);)console.warn("scheduleTimeoutEvent: missed timeout event"),this._resume()},n(e+8)+1)),this.mem.setInt32(e+16,t,!0)},"runtime.clearTimeoutEvent":e=>{e>>>=0;let t=this.mem.getInt32(e+8,!0);clearTimeout(this._scheduledTimeouts.get(t)),this._scheduledTimeouts.delete(t)},"runtime.getRandomData":e=>{e>>>=0,globalThis.crypto.getRandomValues(h(e+8))},"syscall/js.finalizeRef":e=>{e>>>=0;let t=this.mem.getUint32(e+8,!0);if(this._goRefCounts[t]--,this._goRefCounts[t]===0){let a=this._values[t];this._values[t]=null,this._ids.delete(a),this._idPool.push(t)}},"syscall/js.stringVal":e=>{e>>>=0,o(e+24,d(e+8))},"syscall/js.valueGet":e=>{e>>>=0;let t=Reflect.get(r(e+8),d(e+16));e=this._inst.exports.getsp()>>>0,o(e+32,t)},"syscall/js.valueSet":e=>{e>>>=0,Reflect.set(r(e+8),d(e+16),r(e+32))},"syscall/js.valueDelete":e=>{e>>>=0,Reflect.deleteProperty(r(e+8),d(e+16))},"syscall/js.valueIndex":e=>{e>>>=0,o(e+24,Reflect.get(r(e+8),n(e+16)))},"syscall/js.valueSetIndex":e=>{e>>>=0,Reflect.set(r(e+8),n(e+16),r(e+24))},"syscall/js.valueCall":e=>{e>>>=0;try{let t=r(e+8),a=Reflect.get(t,d(e+16)),c=u(e+32),m=Reflect.apply(a,t,c);e=this._inst.exports.getsp()>>>0,o(e+56,m),this.mem.setUint8(e+64,1)}catch(t){e=this._inst.exports.getsp()>>>0,o(e+56,t),this.mem.setUint8(e+64,0)}},"syscall/js.valueInvoke":e=>{e>>>=0;try{let t=r(e+8),a=u(e+16),c=Reflect.apply(t,void 0,a);e=this._inst.exports.getsp()>>>0,o(e+40,c),this.mem.setUint8(e+48,1)}catch(t){e=this._inst.exports.getsp()>>>0,o(e+40,t),this.mem.setUint8(e+48,0)}},"syscall/js.valueNew":e=>{e>>>=0;try{let t=r(e+8),a=u(e+16),c=Reflect.construct(t,a);e=this._inst.exports.getsp()>>>0,o(e+40,c),this.mem.setUint8(e+48,1)}catch(t){e=this._inst.exports.getsp()>>>0,o(e+40,t),this.mem.setUint8(e+48,0)}},"syscall/js.valueLength":e=>{e>>>=0,s(e+16,Number.parseInt(r(e+8).length))},"syscall/js.valuePrepareString":e=>{e>>>=0;let t=b.encode(String(r(e+8)));o(e+16,t),s(e+24,t.length)},"syscall/js.valueLoadString":e=>{e>>>=0;let t=r(e+8);h(e+16).set(t)},"syscall/js.valueInstanceOf":e=>{e>>>=0,this.mem.setUint8(e+24,r(e+8)instanceof r(e+16)?1:0)},"syscall/js.copyBytesToGo":e=>{e>>>=0;let t=h(e+8),a=r(e+32);if(!(a instanceof Uint8Array||a instanceof Uint8ClampedArray)){this.mem.setUint8(e+48,0);return}let c=a.subarray(0,t.length);t.set(c),s(e+40,c.length),this.mem.setUint8(e+48,1)},"syscall/js.copyBytesToJS":e=>{e>>>=0;let t=r(e+8),a=h(e+16);if(!(t instanceof Uint8Array||t instanceof Uint8ClampedArray)){this.mem.setUint8(e+48,0);return}let c=a.subarray(0,t.length);t.set(c),s(e+40,c.length),this.mem.setUint8(e+48,1)},debug:e=>{console.log(e)}}}}async run(s){if(!(s instanceof WebAssembly.Instance))throw new Error("Go.run: WebAssembly.Instance expected");this._inst=s,this.mem=new DataView(this._inst.exports.mem.buffer),this._values=[Number.NaN,0,null,!0,!1,globalThis,this],this._goRefCounts=new Array(this._values.length).fill(Number.POSITIVE_INFINITY),this._ids=new Map([[0,1],[null,2],[!0,3],[!1,4],[globalThis,5],[this,6]]),this._idPool=[],this.exited=!1;let n=4096,r=f=>{let e=n,t=b.encode(`${f}\0`);return new Uint8Array(this.mem.buffer,n,t.length).set(t),n+=t.length,n%8!==0&&(n+=8-n%8),e},o=this.argv.length,h=[];this.argv.forEach(f=>{h.push(r(f))}),h.push(0),Object.keys(this.env).sort().forEach(f=>{h.push(r(`${f}=${this.env[f]}`))}),h.push(0);let d=n;h.forEach(f=>{this.mem.setUint32(n,f,!0),this.mem.setUint32(n+4,0,!0),n+=8}),this._inst.exports.run(o,d),this.exited&&this._resolveExitPromise(),await this._exitPromise}_resume(){if(this.exited)throw new Error("Go program has already exited");this._inst.exports.resume(),this.exited&&this._resolveExitPromise()}_makeFuncWrapper(s){let n=this;return function(){let r={id:s,this:this,args:arguments};return n._pendingEvent=r,n._resume(),r.result}}};var k=(i,s)=>v().transform(i,s),O=(i,s)=>v().parse(i,s),N=(i,s)=>v().convertToTSX(i,s),g,_,C=()=>{g=void 0,_=void 0,globalThis["@astrojs/compiler"]=void 0},D=async i=>{let s=i.wasmURL;if(!s)throw new Error('Must provide the "wasmURL" option');s+="",g||(g=V(s).catch(n=>{throw g=void 0,n})),_=_||await g},v=()=>{if(!g)throw new Error('You need to call "initialize" before calling this');if(!_)throw new Error('You need to wait for the promise returned from "initialize" to be resolved before calling this');return _},W=async(i,s)=>{let n;return WebAssembly.instantiateStreaming?n=await WebAssembly.instantiateStreaming(fetch(i),s):n=await(async()=>{let o=await fetch(i).then(h=>h.arrayBuffer());return WebAssembly.instantiate(o,s)})(),n},V=async i=>{let s=new w,n=await W(i,s.importObject);s.run(n.instance);let r=globalThis["@astrojs/compiler"];return{transform:(o,h)=>new Promise(u=>u(r.transform(o,h||{}))),convertToTSX:(o,h)=>new Promise(u=>u(r.convertToTSX(o,h||{}))).then(u=>({...u,map:JSON.parse(u.map)})),parse:(o,h)=>new Promise(u=>u(r.parse(o,h||{}))).then(u=>({...u,ast:JSON.parse(u.ast)}))}};0&&(module.exports={convertToTSX,initialize,parse,teardown,transform});
